require 'rails_helper'

RSpec.shared_examples_for 'a v3 report' do
  let(:export_date) { Date.new(2021, 1, 1) }

  describe 'exporting' do
    around { |example| travel_to(export_date) { example.run } }

    it 'exports correctly' do
      verify { approval_report_json(report) }
    rescue Goldeen::ApprovalError => e
      received_path = e.received_path
      approved_path = e.approved_path

      message = e.message
      message += 'Approved'
      message += '--------'
      message += File.read(approved_path)
      message += 'Received:'
      message += '--------'
      message += File.read(received_path)
      message += 'Diff:'
      message += '--------'
      message += `diff #{approved_path} #{received_path}`
      message += '--------'

      raise Goldeen::ApprovalError, message
    end
  end
end

# TODO: Eventually account matrix should be the default
RSpec.shared_examples 'v3 report balance matrix testing' do
  before do
    allow_any_instance_of(described_class).to receive(:use_account_matrix?) do
      use_account_matrix?
    end
  end

  context 'with balance matrix' do
    let(:use_account_matrix?) { true }

    it_behaves_like 'a v3 report'
  end

  context 'without balance matrix' do
    let(:use_account_matrix?) { false }

    it_behaves_like 'a v3 report'
  end
end

# Expected
#   AR: $975.00
#   AP: $300.00
#   Draw: ($50.00)
#   Cash: ($50.00)
#   Retained Earnings: $675.00
#     Rent Income: $1,000.00
#     Rent Credit: ($25.00)
#     Electric Expense: $300.00
RSpec.shared_context 'sample activity with contra' do
  before do
    chart = entity.chart_of_accounts

    # Rent Receivable

    accounts_receivable = chart.accounts_receivable

    rent_income = create(:revenue_account,
                         gl_code: 4100,
                         category: 'Rent Income',
                         name: 'Base Rent',
                         tenant: chart)

    create(:journal_entry,
           journal: entity,
           debit_account: accounts_receivable,
           credit_account: rent_income,
           date: export_date,
           amount: '$1,000.00')

    accounts_payable = chart.accounts_payable

    # Rent Credit

    rent_credit = create(:revenue_account,
                         gl_code: 4200,
                         category: 'Rent Income',
                         name: 'Rent Credit',
                         contra: true,
                         tenant: chart)

    create(:journal_entry,
           journal: entity,
           debit_account: rent_credit,
           credit_account: accounts_receivable,
           date: export_date,
           amount: '$25.00')

    # Electric Payable

    electrical_expense = create(:expense_account,
                                gl_code: 5100,
                                category: 'Expense',
                                name: 'Electrical Expense',
                                tenant: chart)

    create(:journal_entry,
           journal: entity,
           debit_account: electrical_expense,
           credit_account: accounts_payable,
           date: export_date,
           amount: '$300.00')

    # Owner Disbursement

    bank_account = create(:asset_account,
                          gl_code: 1500,
                          category: 'Bank',
                          name: 'Bank Account',
                          tenant: chart)

    draw_account = create(:equity_account,
                          gl_code: 3500,
                          category: 'Equity',
                          name: 'Owner Draw',
                          contra: true,
                          tenant: chart)

    create(:journal_entry,
           journal: entity,
           debit_account: draw_account,
           credit_account: bank_account,
           date: export_date,
           amount: '$50.00')
  end
end
