:ruby
  methods = Accounting::FeeManagement::Disbursement::DISBURSEMENT_METHODS
  disbursement_options = methods.except(:none, :manual).map { |k, v| [v, k] }

.ui.small.modal#prepare-disbursement{ data: { controller: 'modal' } }
  %i.black.close.icon
  .header
    Prepare Disbursements
    %span.selected-count
      (0 Selected)
  .content
    .ui.embedded.info.message
      %p
        Clicking submit will prepare an owner disbursement payable for each
        property in the selected entities for their available balance as of the
        activity through date, less their reserve amount.

    = form_with id: :prepare_disbursement_form,
      url: disbursements_path,
      scope: :disbursement,
      local: false,
      remote: true,
      data: { controller: 'background--task-form',
      action: 'ajax:success->modal#close' },
      class: 'ui form' do |f|

      = f.hidden_field :entity_ids, value: nil, id: 'entity_ids'

      .two.fields
        .field
          = f.label :effective_date, 'Activity Through'
          = f.semantic_date_field :effective_date,
            value: Time.zone.today

        .field
          = f.label :disbursement_method
          = f.semantic_dropdown :disbursement_method,
            disbursement_options,
            {},
            class: 'ui search selection dropdown'

      .two.fields
        .field
          = f.label :post_date
          = f.semantic_date_field :post_date,
            value: Time.zone.today

        .field
          = f.label :payables_batch_name
          = f.text_field :payables_batch_name,
            value: "#{Time.zone.today.to_fs(:human_month_day)} Owner Disbursements"

      .ui.error.message

  .actions
    %button.ui.cancel.button
      Cancel
    %button.ui.blue.button{ type: 'submit',
    form: 'prepare_disbursement_form',
    data: { disable: true } }
      Submit

:javascript
  $('#prepare-disbursement-button').click(function (event) {
    event.preventDefault();

    var ids = $('#managed-entities-index')[0]['multi-selection-index'].selectedRowIds();

    $('#prepare-disbursement').find('span.selected-count').text('(' + ids.length + ' Selected)');

    $('#prepare-disbursement').find('input#entity_ids').val(ids);

    $('#prepare-disbursement')
      .modal(modalDefaults)
      .modal('setting', 'autofocus', false)
      .modal('show');
  });
