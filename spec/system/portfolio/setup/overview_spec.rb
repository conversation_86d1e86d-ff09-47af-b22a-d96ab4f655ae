require 'rails_helper'

RSpec.describe 'portfolio setup overview', :js,
               capybara_login: :property_manager do
  let!(:portfolio) { create(:portfolio, setup: false) }

  before { visit portfolio_setup_path(portfolio) }

  scenario 'a user updates the portfolio name' do
    name = 'Updated Name'

    fill_in 'Name', with: name

    expect { click_on 'Next' }.to change { portfolio.reload.name }.to(name)
  end
end
