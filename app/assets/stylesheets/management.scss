//
//= require semantic.min
//= require dragula/dist/dragula.min
//
//= require action_bar
//= require action_grid
//= require action_index
//= require action_table
//= require actiontext
//= require chat
//= require charts
//= require calendar
//= require clearfix
//= require dropdown
//= require reports
//= require report_builder
//= require management/cards
//= require management/feed
//= require management/maintenance_tickets
//= require management/comments
//= require management/contacts
//= require management/tabs
//= require management/taxes
//= require management/nav
//= require leasing
//= require marketing/leads_line_chart
//= require management/mode_wrapper
//= require management/custom_forms
//= require management/pipeline
//= require management/reporting
//= require management/filter_box
//= require management/filter_sidebox
//= require management/action_sidebar
//= require management/financial_statement
//= require management/operations_column
//= require management/operations_pulse
//= require management/calendar_popup
//= require management/projects
//= require maintenance/schedule_work_order
//= require maintenance/work_performed_statement
//= require breadcrumb
//= require paper
//= require printing
//= require check_printing
//= require pulse
//= require statistics
//= require bookings
//= require infinite_list
//= require documents
//= require dropzone
//= require notification
//= require toggle_checkbox
//= require turbolinks
//= require sidebar
//= require payment
//= require project_boards
//= require operations/activity_log
//= require messaging
//= require search
//= require tables
//= require tabs
//= require tunisia
//= require two_factor
//= require message
//= require modals
//= require help_popups
//= require shared/invoice_table
//= require shared/observable_plot
//= require shared/uppy
//= require_tree ./timeline
//
//= require_self
//

@import 'color';
@import url(https://fonts.googleapis.com/css?family=Pacifico);

html,
body {
  height: 100%;
}

.full.height {
  display: flex;
  flex-direction: row;
  height: 100%;

  .main {
    background-color: $off-white;
    box-shadow: 4px 0px 24px -5px #888;
    display: flex;
    flex: 1;
    flex-direction: column;
    height: 100%;
    padding-top: 1em;
    overflow-x: hidden;

    // search bar, avatar dropdown
    >.ui.container {
      flex: 0 0 auto;
    }

    .port {
      height: 100%;
      overflow-y: scroll;
      padding: 1em 0;
      width: 100%;
    }

    .wrapper {
      margin-left: auto;
      margin-right: auto;
      max-width: 1190px;
      margin-bottom: 2em;
    }

    .orange.button {
      background-color: $orange !important;
    }
  }
}

.react-flex {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  overflow-y: hidden;

  >div {
    display: flex;
    flex: 1 1 auto;
    flex-direction: column;
    overflow-y: hidden;
  }
}

.ui.horizontal.divider {
  text-transform: none;
}

.ui.spaced.header {
  display: flex;
  justify-content: space-between;
}

@media only screen and (max-width: 759px) {

  html,
  body {
    background-color: $off-white;
  }
}
