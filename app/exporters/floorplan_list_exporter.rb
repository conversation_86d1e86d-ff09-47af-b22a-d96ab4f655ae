class FloorplanListExporter
  include XLSXExporter

  attr_reader :property

  def initialize(property:)
    @property = property
  end

  def filename
    "#{property.name.parameterize.underscore}_floorplans.xlsx"
  end

  private

  def row_data
    property.floorplans.map do |floorplan|
      [
        floorplan.name,
        floorplan.bedrooms,
        floorplan.bathrooms,
        floorplan.square_feet,
        floorplan.price.format
      ]
    end
  end

  def columns
    {
      'Name' => true,
      'Bedrooms' => true,
      'Bathrooms' => true,
      'Square Feet' => true,
      'Market Rate' => false
    }
  end
end
