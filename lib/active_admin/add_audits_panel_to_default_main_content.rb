module ActiveAdmin
  module AddAuditsPanelToDefaultMainContent
    def audits_panel(auditable = resource, title: nil, &block)
      title ||= "History for #{resource.model_name.human} #{resource.id}"

      audits = if block_given?
                 yield
               else
                 auditable.own_and_associated_audits.reorder(created_at: :asc).preload(:user)
               end

      panel title do
        # TODO: Use a ViewComponent
        render partial: 'shared/audit_log', locals: { audits: audits, reverse: false }
      end
    end

    def default_main_content
      super

      audits_panel if resource.respond_to?(:audits)
    end
  end
end
