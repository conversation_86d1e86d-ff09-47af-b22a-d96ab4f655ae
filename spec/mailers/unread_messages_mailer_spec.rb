require 'rails_helper'

RSpec.describe UnreadMessages<PERSON>ail<PERSON>, capybara_login: :property_manager do
  describe 'notify' do
    let(:notification) { create(:notification) }
    let(:sender) { notification.description }
    let(:user) { notification.user }
    let(:body) { notification.title }
    let(:link) { notification.link }
    let(:mail) { described_class.notify(user, [notification]) }

    before { Customer.update_all(subdomain: 'alever') }

    it 'is send to the user' do
      expect(mail.to).to eq([user.email])
    end

    it 'includes the message sender in the subject' do
      expect(mail.subject).to include(sender)
    end

    it 'includes a link to the message in the body' do
      expect(mail.body.encoded).to include(link)
    end

    it 'includes the body of the message in the body' do
      expect(mail.body.encoded).to include(body)
    end
  end
end
