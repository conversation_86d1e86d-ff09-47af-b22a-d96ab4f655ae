require 'rails_helper'

RSpec.describe Api::V1::WorkOrdersController,
               api_login: :property_manager do
  describe 'GET #regardable' do
    it 'returns http success' do
      create(:unit)

      get :regardable, format: :json

      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #create' do
    it 'returns http success' do
      unit = create(:unit)

      subject = 'Broken Light Fixture'
      description = 'Needs rewire'

      post :create, format: :json, params: {
        work_order: {
          subject: subject,
          description: description,
          regarding: unit.to_sgid.to_s,
          urgency: 'minor'
        }
      }

      expect(response).to have_http_status(:success)

      work_order = MaintenanceTicket.last!
      expect(work_order).to be_minor
      expect(work_order.subject).to eq(subject)
      expect(work_order.description).to eq(description)
      expect(work_order.opened_by).to eq(manager)
      expect(work_order.regarding).to eq(unit)
    end
  end

  context 'with an existing work order' do
    let!(:work_order) do
      create(:maintenance_ticket, status: :seen).tap do |work_order|
        work_order.events.comment.create!(author: manager, body: 'Hello')
        work_order.close!
      end
    end

    describe 'GET #show json' do
      it 'returns http success' do
        get :show, format: :json, params: { id: work_order.id }
        expect(response).to have_http_status(:success)
      end
    end

    describe 'GET #index json' do
      it 'returns http success' do
        get :index, format: :json
        expect(response).to have_http_status(:success)

        wo = JSON.parse(response.body, symbolize_names: true)[0]

        expect(wo[:title]).to eq work_order.subject
        expect(wo[:available_actions]).to eq(['reopen'])
      end
    end

    describe 'POST #comment json' do
      it 'returns http success' do
        body = 'My Comment'

        post :comment, params: {
          id: work_order.id,
          work_order: {
            comment: body
          }
        }

        expect(response).to have_http_status(:success)

        comment = work_order.events.comment.last!
        expect(comment.author).to eq(manager)
        expect(comment.body).to eq(body)
      end
    end

    describe 'POST #assess json' do
      it 'returns http success' do
        post :assess, params: {
          id: work_order.id,
          work_order: {
            assessment: 'My Assessment'
          }
        }

        expect(response).to have_http_status(:success)

        work_order.reload
        expect(work_order).to be_assessed
        expect(work_order.assessed_by).to eq(manager)
      end
    end

    describe 'POST #close json' do
      before { work_order.reopen! }

      it 'returns http success' do
        post :close, params: {
          id: work_order.id,
          work_order: {
            closing_comment: 'Closing Comment'
          }
        }

        expect(response).to have_http_status(:success)

        work_order.reload
        expect(work_order).to be_closed
      end
    end

    describe 'POST #resolve json' do
      before do
        work_order.property.configuration.update!(
          maintenance_billing_kind: :billed_individually
        )
        work_order.reopen!
      end

      it 'returns http success' do
        post :resolve, params: {
          id: work_order.id,
          work_order: {
            resolving_comment: 'Resolving Comment'
          }
        }

        expect(response).to have_http_status(:success)

        work_order.reload
        expect(work_order).to be_resolved
        expect(work_order.events.last.body).to eq 'Resolving Comment'
      end
    end

    describe 'POST #photo json' do
      let(:direct_upload_url) { 'http://direct.upload.url' }

      let(:params) do
        {
          id: work_order.id,
          photo: { direct_upload_url: direct_upload_url }
        }
      end

      it 'returns http success' do
        post :photo, params: params
        expect(response).to have_http_status(:success)

        photo = work_order.photos.last!
        expect(photo.direct_upload_url).to eq(direct_upload_url)
        expect(photo.uploaded_by).to eq(manager)
      end
    end

    describe 'POST #assign_vendor json' do
      let(:vendor) { create(:vendor) }

      it 'returns http success' do
        post :assign_vendor, params: {
          id: work_order.id,
          vendor_id: vendor.id,
          note: 'Note to vendor'
        }

        expect(response).to have_http_status(:success)

        assignment = work_order.vendor_assignments.last!
        expect(assignment.vendor).to eq(vendor)
      end
    end
  end
end
