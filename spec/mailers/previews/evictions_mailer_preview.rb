class EvictionsMailerPreview < ActionMailer::Preview
  def notice_to_quit
    EvictionsMailer.notice_to_quit(tenant, lease, document, balance.cents)
  end

  def demand_for_possession
    EvictionsMailer.demand_for_possession(tenant, lease, document, balance.cents)
  end

  private

  def lease
    Lease.active.first
  end

  def tenant
    lease.primary_tenant
  end

  def document
    nil
  end

  def balance
    lease.chain.aging_delinquency.overdue
  end
end
