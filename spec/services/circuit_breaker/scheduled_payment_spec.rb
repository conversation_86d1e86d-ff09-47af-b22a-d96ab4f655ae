require 'rails_helper'

RSpec.describe CircuitBreaker::ScheduledPayment do
  let!(:scheduled_payment) { create(:scheduled_payment) }
  let(:today) { Time.zone.today }

  it 'prevents a 2nd run' do
    expect(described_class.check(scheduled_payment, today)).not_to be_broken

    create(:payment, scheduled_payment: scheduled_payment, date: today)
    expect(described_class.check(scheduled_payment.reload, today)).to be_broken

    expect(described_class.check(scheduled_payment, today + 1.day)).not_to \
      be_broken
  end
end
