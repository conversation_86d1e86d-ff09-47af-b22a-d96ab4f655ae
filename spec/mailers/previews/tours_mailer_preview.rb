class ToursMailerPreview < ActionMailer::Preview
  def guided_tour_scheduled
    tour = FactoryBot.build_stubbed(:tour)
    ToursMailer.tour_scheduled(tour)
  end

  def guided_tour_followup
    tour = FactoryBot.build_stubbed(:tour, time: 5.hours.ago)
    ToursMailer.tour_followup(tour)
  end

  def self_guided_tour_scheduled
    tour = FactoryBot.build_stubbed(:tour, :self_guided)
    ToursMailer.tour_scheduled(tour)
  end

  def self_guided_tour_followup
    tour = FactoryBot.build_stubbed(:tour, :self_guided, time: 5.hours.ago)
    ToursMailer.tour_followup(tour)
  end
end
