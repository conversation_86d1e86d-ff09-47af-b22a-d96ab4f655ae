class CreateListingPublishEvents < ActiveRecord::Migration[6.1]
  def change
    create_table :listing_publish_events do |t|
      t.references :listing, null: false, foreign_key: true
      t.datetime :timestamp, null: false, default: -> { 'NOW()' }
      t.integer :direction, null: false

      t.timestamps
    end

    reversible do |direction|
      direction.up do
        Listing.published.find_each do |listing|
          timestamp = listing.updated_at # This is an approximation.

          listing.publish_events.published.create!(timestamp: timestamp)
        end
      end
    end
  end
end
