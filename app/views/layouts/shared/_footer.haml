%footer
  .ui.very.basic.inverted.segment{ style: 'background-color: #425b76; color: #809bb8' }
    .ui.container
      .ui.two.column.grid
        .middle.aligned.column
          &copy;
          = Time.zone.now.year
          = succeed '.' do
            = Customer.current&.name || 'Revela, Inc'
          All Rights Reserved.

        .right.aligned.middle.aligned.column
          = link_to 'https://www.revela.co', target: '_blank' do
            %img.ui.small.right.floated.image{ src: '//revela-public.s3.amazonaws.com/landing/images/logo_text_white.svg', style: 'opacity: .4;' }

= render partial: 'shared/mouseflow'
