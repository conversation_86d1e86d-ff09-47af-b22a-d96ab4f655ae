module LineItem::ImpactsCashBasis
  extend ActiveSupport::Concern

  included do
    with_options if: :impacts_cash_basis? do
      after_update :register_line_item_changed_for_cash_basis!
      before_destroy :register_line_item_changed_for_cash_basis!
      before_commit :regenerate_associated_cash_basis_entries!
    end
  end

  private

  def impacts_cash_basis?
    Plutus.writes_persisted_cash_basis?
  end

  def register_line_item_changed_for_cash_basis!
    payment_ids = invoice.invoice_payments.pluck(:payment_id)

    Accounting::CashBasis::AssociatedRejournaling
      .register_changed_payment_ids! { payment_ids }
  end

  def regenerate_associated_cash_basis_entries!
    Accounting::CashBasis::AssociatedRejournaling.regenerate_cash_basis!
  end
end
