class InvoiceProcessingPreview < ActionMailer::Preview
  def receipt
    company = Company.first
    to = "#{company.subdomain}@invoices.revela.co"
    invoice_processing_email = FactoryBot.build(:invoice_processing_email, to: to)
    InvoiceProcessingMailer.receipt(invoice_processing_email)
  end

  def unknown_customer
    recipient = '<EMAIL>'
    destination = '<EMAIL>'
    InvoiceProcessingMailer.unknown_customer(recipient, destination)
  end

  def notify_processor
    processor = InvoiceProcessing::User.first

    unprocessed = {
      'ccp' => 4,
      'chiohc' => 10,
      'gebraelmgmt' => 1
    }

    new_unprocessed = {
      'ccp' => InvoiceProcessing::Email.take(4),
      'chiohc' => InvoiceProcessing::Email.last(5)
    }

    totals = {
      unprocessed: 15,
      new_unprocessed: 9
    }

    InvoiceProcessingMailer.notify_processor(
      user: processor,
      unprocessed: unprocessed,
      new_unprocessed: new_unprocessed,
      totals: totals
    ).deliver
  end
end
