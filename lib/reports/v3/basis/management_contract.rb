class Reports::V3::Basis::ManagementContract < Reports::V3::Basis::Base
  def relation
    ManagementContract.all
  end

  def joined(query)
    query = query.references(:company).includes(:company)

    query = query.includes(company: :tags) if mapping.key?(:entity_tags)

    query
  end

  def ordered(query)
    query.merge(Company.order(name: :asc))
  end

  def scoped(query)
    scope.apply(query) { |s| where(company: s.companies) }
  end

  def filtered(query)
    case filters.status
    when 'archived' then query.merge(Company.archived)
    when 'unarchived' then query.merge(Company.unarchived)
    else
      query
    end
  end

  column :contract do
    {
      value: 'View',
      link: manage_company_management_contract_path(company)
    }
  end

  column :entity do
    {
      value: company.name,
      link: manage_company_path(company)
    }
  end

  column :start_date do
    { value: start_date }
  end

  column :end_date do
    { value: end_date }
  end

  column :executed_at do
    { value: executed_at }
  end

  column :rent_fee do
    {
      value: rent_markup,
      justify: :right
    }
  end

  column :lease_fee do
    {
      value: new_lease_markup,
      justify: :right
    }
  end

  column :renewal_fee do
    {
      value: renewal_markup,
      justify: :right
    }
  end

  column :labor_markup do
    {
      value: labor_markup,
      justify: :right
    }
  end

  column :materials_markup do
    {
      value: material_markup,
      justify: :right
    }
  end

  column :maintenance_limit do
    {
      value: maintenance_limit,
      justify: :right
    }
  end

  column :payables_limit do
    {
      value: payables_limit,
      justify: :right
    }
  end

  column :minimum_amount do
    {
      value: minimum_amount,
      justify: :right
    }
  end

  column :billed_through do
    date = company.management_fees_billed_through

    {
      value: date&.to_fs(:short_date) || 'Never',
      sort: date&.to_datetime&.to_i,
      justify: :center
    }
  end

  column :entity_created_at do
    { value: company.created_at.to_date, justify: :right }
  end

  column :entity_tags do
    { value: company.tags.pluck(:tag).sort.join(', '), justify: :center }
  end
end
