require 'rails_helper'

RSpec.describe ActiveModel::CocoonModel do
  it 'supports singular associations and validations' do
    form_model = Class.new do
      include ActiveModel::CocoonModel

      attr_accessor :first_name

      accepts_nested_attributes_for :address, 'Address'

      validates_associated :address

      def self.name
        'SampleForm'
      end
    end

    params = ActionController::Parameters.new(
      form: {
        first_name: 'Sample',
        address_attributes: {
          line_one: '123 Main St'
        }
      }
    )

    attributes = params
                 .require(:form)
                 .permit(:first_name, address_attributes: [:line_one])

    form = form_model.new(attributes)

    expect(form.first_name).to eq('Sample')
    expect(form.address.line_one).to eq('123 Main St')
    expect(form).not_to be_valid
    expect(form.errors[:address]).to include(/city can't be blank/i)
  end

  it 'supports collection associations and validations' do
    form_model = Class.new do
      include ActiveModel::CocoonModel

      accepts_nested_attributes_for :line_items, 'LineItem', collection: true

      validates_associated :line_items

      def self.name
        'SampleForm'
      end
    end

    params = ActionController::Parameters.new(
      form: {
        line_items_attributes: [
          {
            description: 'Sample Item'
          }
        ]
      }
    )

    attributes = params
                 .require(:form)
                 .permit(line_items_attributes: [:description])

    form = form_model.new(attributes)

    expect(form.line_items.first.description).to eq('Sample Item')
    expect(form).not_to be_valid
    expect(form.errors[:line_item]).to include(/unit price cents/i)
  end
end
