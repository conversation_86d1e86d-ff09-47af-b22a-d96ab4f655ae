class DataExporting::ExportLeaseChain
  extend Service

  attr_reader :lease_chain

  def initialize(lease_chain, user:)
    @lease_chain = lease_chain
  end

  def call
    Dir.mktmpdir do |directory|
      export_ledger(directory)

      lease_chain.leases.each do |lease|
        export_lease_summary(lease, directory)
        export_lease_document(lease, directory)
        export_itemized_damages(lease, directory)
      end

      yield directory if block_given?
    end

    OpenStruct.new(successful?: true)
  end

  private

  def export_ledger(directory)
    DataExporting::Ledger
      .new(lease_chain: lease.chain)
      .export(directory: directory)
  end

  def export_lease_document(lease, directory)
    DataExporting::LeaseDocument
      .new(lease: lease)
      .export(directory: directory)
  end

  def export_lease_summary(lease, directory)
    DataExporting::LeaseSummary
      .new(lease: lease)
      .export(directory: directory)
  end

  def export_itemized_damages(lease, directory)
    itemized_damages = lease.processed_move_out&.itemized_damages

    return unless itemized_damages

    DataExporting::ItemizedDamages
      .new(itemized_damages: itemized_damages)
      .export(directory: directory)
  end
end
