class Api::V2::ListingResource < JSONAPI::Resource
  include Api::V2::Shared

  attributes :name, :description, :amenities
  attributes :market_rate, delegate: :price
  attributes :bedrooms, :bathrooms, :square_feet
  attribute :date_available
  attributes :published, :published_at
  attribute :photo_urls
  attributes :created_at, :updated_at

  has_one :floorplan

  def photo_urls
    @model.shrine_photos.map(&:upload_url)
  end

  def self.records_base(options = {})
    super(options).includes(:floorplan, :publish_events, :shrine_photos)
  end

  def self.sortable_fields(context)
    []
  end

  def self.user_records(relation, user)
    ListingsQuery.new(relation).search.by_user(user)
  end
end
