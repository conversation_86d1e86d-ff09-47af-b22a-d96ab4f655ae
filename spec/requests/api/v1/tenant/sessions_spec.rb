require 'rails_helper'

RSpec.describe 'api v1 tenant sessions' do
  let(:tenant) { create(:tenant) }

  before { host! 'public.lvh.me' }

  describe 'creating new sessions' do
    before do
      post api_v1_tenant_sessions_path, params: {
        email: tenant.email, password: password
      }
    end

    context 'with valid credentials' do
      let(:password) { tenant.password }

      it 'returns an auth token' do
        expect(response).to have_http_status(:created)
      end
    end

    context 'with invalid credentials' do
      let(:password) { 'Incorrect' }

      it 'returns unauthorized' do
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
