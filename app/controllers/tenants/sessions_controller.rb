class Tenants::SessionsController < Devise::SessionsController
  include CustomerInstanceRequiredController
  include LoginBrandController

  layout 'simple_login'

  # before_filter :configure_sign_in_params, only: [:create]

  # GET /resource/sign_in
  # def new
  #   super
  # end

  # POST /resource/sign_in
  # def create
  #   super
  # end

  # DELETE /resource/sign_out
  # def destroy
  #   super
  # end

  protected

  def after_sign_in_path_for(resource)
    tenants_dashboard_path
  end

  def after_sign_out_path_for(resource)
    new_tenant_session_path
  end

  # If you have extra params to permit, append them to the sanitizer.
  # def configure_sign_in_params
  #   devise_parameter_sanitizer.for(:sign_in) << :attribute
  # end
end
