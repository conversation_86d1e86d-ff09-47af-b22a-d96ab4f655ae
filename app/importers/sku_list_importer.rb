class SkuListImporter
  extend Service
  include Importers::XLSX

  attr_reader :vendor, :path

  def initialize(vendor:, path:)
    @vendor = vendor
    @path = path
  end

  def call
    items = rows.drop(1).map do |row|
      create_sku_item(row)
    end

    OpenStruct.new(successful?: true, items: items)
  rescue ActiveRecord::RecordInvalid => e
    OpenStruct.new(successful?: false, errors: e.record.errors.full_messages)
  end

  private

  def create_sku_item(row)
    vendor.sku_list_items.create!(
      category: row.value('Category'),
      brand: row.value('Brand'),
      description: row.value('Description'),
      sku: row.value('SKU'),
      price: row.value('Price'),
      labor: row.value('Labor')
    )
  end
end
