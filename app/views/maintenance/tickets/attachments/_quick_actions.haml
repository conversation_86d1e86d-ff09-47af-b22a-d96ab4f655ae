:ruby
  if grid_view
    switch_view_icon = 'list'
  else
    switch_view_icon = 'th'
  end

.inline
  = link_to download_all_path,
    class: 'ui icon button with-popup',
    remote: true,
    data: { content: 'Download All' } do
    %i.icon.download

  = link_to switch_path,
    class: 'ui icon button with-popup',
    data: { content: 'Switch View' } do
    %i.icon{ class: switch_view_icon }

:javascript
  $('.with-popup').popup()
