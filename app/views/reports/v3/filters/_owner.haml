- unless report_user.is_a?(Owner)
  :ruby
    owner_options = OwnersQuery
                    .new.search
                    .by_user(report_user)
                    .unarchived
                    .order(first_name: :asc)
                    .map do |owner|
      display = if owner.email.present?
                  "#{owner.name} (#{owner.email})"
                else
                  owner.name
                end

      [display, owner.id]
    end

  .field
    = f.label :owner_id, 'Owner Contact'
    = f.semantic_dropdown :owner_id,
      [['All Owners', '']] + owner_options,
      { selected: params.dig(:filters, :owner_id) },
      class: 'ui search selection dropdown',
      data: { options: { placeholder: 'All Owners' } }
