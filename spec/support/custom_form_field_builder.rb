module CustomFormFieldBuild<PERSON>
  def create_message_form_field(form:, content: 'message', row: 1, order: 1)
    element = create(
      :custom_forms_element_message,
      content: content,
      form_field: build(
        :custom_forms_form_field,
        form: form,
        row: row,
        order: order,
        removable: true
      )
    )

    element.form_field
  end

  def create_section_header_form_field(
    form:,
    content: 'section header',
    row: 1,
    order: 1
  )

    element = create(
      :custom_forms_element_section_header,
      content: content,
      form_field: build(
        :custom_forms_form_field,
        form: form,
        row: row,
        order: order,
        removable: true
      )
    )

    element.form_field
  end

  def create_short_text_form_field(
    form:,
    label: 'label',
    required: false,
    row: 1,
    order: 1
  )

    element = create(
      :custom_forms_element_short_text,
      label: label,
      required: required,
      placeholder: 'placeholder',
      form_field: build(
        :custom_forms_form_field,
        form: form,
        row: row,
        order: order,
        removable: !required
      )
    )

    element.form_field
  end

  def create_single_checkbox_form_field(
    form:,
    label: 'label',
    required: false,
    row: 1,
    order: 1
  )

    element = create(
      :custom_forms_element_single_check_box,
      label: label,
      required: required,
      form_field: build(
        :custom_forms_form_field,
        form: form,
        row: row,
        order: order,
        removable: !required
      )
    )

    element.form_field
  end

  def create_date_form_field(
    form:,
    label: 'label',
    required: false,
    row: 1,
    order: 1
  )

    element = create(
      :custom_forms_element_date,
      label: label,
      required: required,
      form_field: build(
        :custom_forms_form_field,
        form: form,
        row: row,
        order: order,
        removable: !required
      )
    )

    element.form_field
  end

  def create_email_form_field(
    form:,
    label: 'label',
    required: false,
    row: 1,
    order: 1
  )
    element = create(
      :custom_forms_element_email,
      label: label,
      required: required,
      placeholder: '<EMAIL>',
      form_field: build(
        :custom_forms_form_field,
        form: form,
        row: row,
        order: order,
        removable: !required
      )
    )

    element.form_field
  end

  def create_phone_number_form_field(
    form:,
    label: 'label',
    required: false,
    row: 1,
    order: 1
  )
    element = create(
      :custom_forms_element_phone_number,
      label: label,
      required: required,
      placeholder: '************',
      form_field: build(
        :custom_forms_form_field,
        form: form,
        row: row,
        order: order,
        removable: !required
      )
    )

    element.form_field
  end
end
