class ActionTable::Roles < ActionTable::Base
  def columns
    [name, users, created_at, updated_at, edit, delete]
  end

  private

  def name
    {
      title: 'Name',
      align: :left,
      value: ->(role) { role.name },
      link: ->(role) { organization_role_path(role) }
    }
  end

  def users
    {
      title: 'Users',
      align: :right,
      value: ->(role) { role.active_user_count },
      sort: ->(role) { role.active_user_count },
      width: :two
    }
  end

  def created_at
    {
      title: 'Created',
      value: ->(role) { role.created_at.to_fs(:short_datetime) },
      sort: ->(role) { role.created_at.to_i }
    }
  end

  def updated_at
    {
      title: 'Updated',
      value: ->(role) { role.updated_at.to_fs(:short_datetime) },
      sort: ->(role) { role.updated_at.to_i }
    }
  end

  def edit
    {
      title: 'Actions',
      value: ->(_) { 'Edit' },
      link: ->(role) { edit_organization_role_path(role) },
      colspan: 2,
      width: :three,
      permission: can.update_organization_roles?
    }
  end

  def delete
    {
      title: '',
      value: ->(_) { 'Delete' },
      link: ->(role) { organization_role_path(role) },
      link_options: lambda do |_|
        { data: { method: :delete, confirm: 'Delete Role?' } }
      end,
      colspan: 0,
      permission: can.destroy_organization_roles?
    }
  end
end
