.ui.basic.inverted.padded.hero-gradient.segment
  .header-image.services
  .orange-overlay
  .ui.container
    %h1.ui.heavy.header
      = meta_title 'Services'
    %p.description
      = meta_description 'In addition to providing software, we provide clients with consultative |
      and business services.' |

.ui.basic.padded.gray-circles.product.segment#services-professional
  .ui.container
    .ui.vertical.two.column.stackable.grid
      .column
        %h1.ui.header
          Real Estate Professional Services

        %p.description
          We offer due diligence services and property inspection services,
          real estate consulting, onboarding support, and other professional
          services for the real estate and asset management industry. These
          are custom tailored to each of our client’s specific needs.
      .column
        %img.ui.feature.image{ src: '//revela-public.s3.amazonaws.com/landing/images/professional_services.jpeg' }

.ui.basic.padded.white-circles.product.segment#services-web
  .ui.container
    .ui.vertical.two.column.stackable.mobile.reversed.grid
      .column
        %img.ui.feature.image{ src: '//revela-public.s3.amazonaws.com/landing/images/web_development.png' }
      .column
        %h1.ui.header
          Web Development

        %p.description
          We build clients websites that can be directly integrated with their
          revela environment. Each website or application can be developed to
          suite our individual clients needs.

.ui.basic.padded.grey-circles.product.segment#services-automation
  .ui.container
    .ui.vertical.two.column.stackable.grid
      .column
        %h1.ui.header
          Process Automation

        %p.description
          Using the Revela platform as a foundation, we can customize
          technology and software solutions that can help you automate all
          aspects of real estate and asset management.
      .column
        %img.ui.feature.image{ src: '//revela-public.s3.amazonaws.com/landing/images/vendor_assignment.png' }
