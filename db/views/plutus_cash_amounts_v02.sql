-- Amounts from direct journal entries
WITH direct_amounts AS (
  SELECT
    plutus_amounts.*
  FROM
    plutus_amounts
  JOIN
   plutus_entries ON plutus_entries.id = plutus_amounts.entry_id AND plutus_entries.commercial_document_id IS NULL
),

-- payment id, prorated float paid
payment_prorations AS (
  SELECT
    payments.id AS payment_id,
    invoice_payments.amount_cents::real / COALESCE(NULLIF(payments.amount_cents, 0), 1)::real AS prorating_factor,
    invoice_payments.id as invoice_payment_id
  FROM
    payments
  JOIN
    invoice_payments ON invoice_payments.payment_id = payments.id
),

indirect_payment_amounts AS (
  SELECT
    plutus_amounts.id,
    plutus_amounts.type,
    plutus_amounts.account_id,
    (payment_prorations.invoice_payment_id * -1) AS entry_id,
    ROUND(plutus_amounts.amount * payment_prorations.prorating_factor) AS amount,
    plutus_amounts.reconciliation_id
  FROM
    plutus_amounts
  JOIN
    plutus_entries ON plutus_entries.id = plutus_amounts.entry_id -- to find payment
  JOIN
    payments ON payments.id = plutus_entries.commercial_document_id AND plutus_entries.commercial_document_type = 'Payment'
  JOIN
    payment_prorations ON payment_prorations.payment_id = payments.id
),

-- invoice id, prorated float paid, originating payment id
invoice_prorations AS (
  SELECT
    invoices.id AS invoice_id,
    invoice_payments.amount_cents::real / COALESCE(NULLIF(invoice_total_amounts.amount, 0), 1)::real AS prorating_factor,
    plutus_entries.id AS payment_entry_id,
    invoice_payments.id as invoice_payment_id
  FROM
    invoices
  JOIN
    invoice_payments ON invoice_payments.invoice_id = invoices.id
  JOIN
    payments ON invoice_payments.payment_id = payments.id
  JOIN
    plutus_entries ON plutus_entries.commercial_document_id = payments.id AND plutus_entries.commercial_document_type = 'Payment'
  JOIN
    invoice_total_amounts ON invoice_total_amounts.invoice_id = invoices.id
),

-- amounts for invoices prorated by payment ratio
indirect_invoice_amounts AS (
  SELECT
    plutus_amounts.id,
    plutus_amounts.type,
    plutus_amounts.account_id,
    (invoice_prorations.invoice_payment_id * -1) AS entry_id,
    ROUND(plutus_amounts.amount * invoice_prorations.prorating_factor) AS amount,
    plutus_amounts.reconciliation_id
  FROM
    plutus_amounts
  JOIN
    plutus_entries ON plutus_entries.id = plutus_amounts.entry_id -- to find invoice
  JOIN
    invoices ON invoices.id = plutus_entries.commercial_document_id AND plutus_entries.commercial_document_type = 'Invoice'
  JOIN
    invoice_prorations ON invoice_prorations.invoice_id = invoices.id
),

all_amounts AS (
  SELECT * FROM direct_amounts
  UNION ALL
  SELECT * FROM indirect_payment_amounts
  UNION ALL
  SELECT * FROM indirect_invoice_amounts
)

SELECT
  all_amounts.*
FROM
  all_amounts
JOIN
  plutus_cash_accounts
ON
  all_amounts.account_id = plutus_cash_accounts.id
;
