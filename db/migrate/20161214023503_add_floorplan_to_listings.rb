class AddFloorplanToListings < ActiveRecord::Migration[5.0]
  def change
    reversible do |direction|
      direction.up do
        add_reference :listings, :floorplan, foreign_key: true

        Listing.find_each do |listing|
          target = listing.target_type.constantize.find(listing.target_id)
          if target.is_a? Property
            listing.update(floorplan: target.floorplans.first)
          else
            listing.update(floorplan: target)
          end
        end

        change_column_null :listings, :floorplan_id, null: false
      end

      direction.down do
        remove_reference :listings, :floorplan, foreign_key: true
      end
    end

    remove_reference :listings, :target, polymorphic: true
  end
end
