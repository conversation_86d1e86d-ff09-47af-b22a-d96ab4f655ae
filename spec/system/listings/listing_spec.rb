require 'rails_helper'

RSpec.describe 'looking at a listing', :js do
  let(:leases) { [] }
  let!(:unit) { create(:unit, leases: leases) }
  let!(:listing) { create(:listing, :published, floorplan: unit.floorplan) }

  before { visit listing_path(listing) }

  describe 'date available' do
    context 'available now' do
      scenario 'a user sees the date available on a listing' do
        expect(page).to have_content(/available now/i)
      end
    end

    context 'available soon' do
      let(:leases) do
        [
          build(:lease,
                kind: :fixed,
                start_date: 1.year.ago,
                end_date: 1.month.from_now)
        ]
      end

      scenario 'a user sees the date available on a listing' do
        expect(page).to \
          have_content(/available #{1.month.from_now.to_fs(:human_date)}/i)
      end
    end

    context 'unavailable' do
      let(:leases) do
        [build(:lease, kind: :month_to_month, start_date: 3.months.ago)]
      end

      scenario 'a user sees the date available on a listing' do
        expect(page).to have_content(/contact for availability/i)
      end
    end
  end
end
