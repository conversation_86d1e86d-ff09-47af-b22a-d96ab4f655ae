module Importers::V3::Base::Validation
  private

  def validate(&)
    results = validators.map do |validator|
      validator.validate(&).tap do |result|
        result.errors.each do |error|
          # Fail immediately on unrecoverable errors
          fail error if error.is_a?(Importers::V3::Errors::UnrecoverableError)
        end
      end
    end

    result = results.reduce(&:|)

    return if result.valid?

    # For now, UnrecoverableError until we support partial imports.
    fail Importers::V3::Errors::UnrecoverableError, validation_error_message_block(result)
  end

  def validators
    importer = self
    schema = importer.class.schema

    validator_klasses.map do |validator_klass|
      validator_klass.new(importer:, parser:, schema:, logger:)
    end
  end

  def validator_klasses
    [
      Importers::V3::Schema::Validator::RequiredColumns,
      (
        if Feature.enabled?(:importers_row_schema_validation, Customer.current)
          Importers::V3::Schema::Validator::RowSchema
        end
      )
    ].compact
  end

  def validation_error_message_block(result)
    max_row_display = 50

    all_errors = result.errors

    strings = all_errors.group_by(&:identifier).flat_map do |identifier, identifier_errors|
      column_name = identifier.to_s.titleize # TODO: look at column name in import

      identifier_errors.group_by(&:issue).map do |issue, errors|
        row_numbers = errors.map(&:row).sort.uniq
        count = row_numbers.count

        row_numbers_display = row_numbers.take(max_row_display).to_sentence
        row_numbers_display += '...' if count > max_row_display

        issue += " (#{count})" if count > 1
        issue += ':'

        [
          column_name,
          issue.humanize.downcase,
          'Row'.pluralize(count),
          row_numbers_display
        ].compact.join(' ')
      end
    end

    strings.sort.join("\n")
  end
end
