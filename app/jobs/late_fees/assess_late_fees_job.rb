##
# Job to assess late fees on a given day. To be run after 7am at minimum once
# per customer per day. Enqueues {LateFees::AssessLateFeePresetJob} once for
# each late fee preset.
class LateFees::AssessLateFeesJob < ApplicationJob
  queue_as :within_one_hour

  def perform
    ActiveRecord::Base.transaction do
      return if already_ran_today?

      return if too_early_to_run?

      late_fee_presets.each do |preset|
        LateFees::AssessLateFeePresetJob.perform_later(preset, Time.zone.today)
      end

      Event.create!(topic: 'late_fees_generated')
    end
  end

  private

  def already_ran_today?
    last_ran_at = Event.order(created_at: :desc)
                       .where(topic: 'late_fees_generated')
                       .first&.created_at

    return false unless last_ran_at

    last_ran_at.to_date == Time.zone.today
  end

  def too_early_to_run?
    Time.zone.now.hour < 7
  end

  def late_fee_presets
    ChargePreset.late_fee
  end
end
