require 'rails_helper'

# Additional specs to check for correctness when billing the following months
# rent on the 25th (not pre post)
RSpec.describe ChargeSchedule::Bill do
  let(:date) { Date.new(2023, 3, 25) }

  around { |example| travel_to(date) { example.run } }

  def configure_rent_schedule(configuration)
    # Post 25th, due 25th, no pre post
    create(:charge_preset, :rent, configuration: configuration, net_d: 0)

    month_to_month_preset = create(:charge_preset,
                                   :lease,
                                   configuration: configuration,
                                   amount: '$25.00')

    configuration.update!(
      rent_due_day: 25,
      rent_pre_post: 0,
      month_to_month_charge_preset: month_to_month_preset
    )
  end

  # Expect that April charge schedule entries are used when posting (not
  # prepost) April rent in March
  it 'uses the appropriate dates when billing on the 25th' do
    charge_schedule = create(:charge_schedule,
                             invoiced_through: Date.new(2023, 3, 31))

    lease = charge_schedule.chargeable

    configure_rent_schedule(lease.configuration)

    lease.update!(
      start_date: Date.new(2023, 3, 1),
      end_date: Date.new(2023, 4, 30)
    )

    expect(charge_schedule.entries).to be_empty

    # First month, of two months, should not bill
    create(:charge_schedule_entry,
           lease: lease,
           charge_schedule: charge_schedule,
           amount: '$100.00',
           start_date: Date.new(2023, 3, 1),
           end_date: Date.new(2023, 3, 31))

    # Second month, of two months, should bill
    create(:charge_schedule_entry,
           lease: lease,
           charge_schedule: charge_schedule,
           amount: '$150.00',
           start_date: Date.new(2023, 4, 1),
           end_date: Date.new(2023, 4, 30))

    result = described_class.call(charge_schedule.reload)
    expect(result.invoices.size).to eq(1)

    # April entry should have billed
    invoice = result.invoices.first
    expect(invoice.amount.format).to eq('$150.00')
    expect(invoice.description).to eq('April Rent')

    # Charge schedule should be billed through April
    expect(charge_schedule.reload.invoiced_through).to eq(Date.new(2023, 4, 30))

    # Already billed
    result = described_class.call(charge_schedule.reload)
    expect(result.invoices.size).to eq(0)
  end

  # Expect April prorated first months rent to bill in March
  it 'appropriately bills prorated first months rent' do
    charge_schedule = create(:charge_schedule)

    lease = charge_schedule.chargeable

    configure_rent_schedule(lease.configuration)

    lease.update!(
      start_date: Date.new(2023, 4, 16),
      end_date: Date.new(2023, 5, 31)
    )

    create(:charge_schedule_entry,
           lease: lease,
           charge_schedule: charge_schedule,
           amount: '$100.00',
           start_date: lease.start_date,
           end_date: lease.end_date)

    result = described_class.call(charge_schedule.reload)
    expect(result.invoices.size).to eq(1)

    # April prorated rent
    invoice = result.invoices.first
    expect(invoice.amount.format).to eq('$50.00')
    expect(invoice.description).to eq('April Rent')

    # Charge schedule should be billed through April
    expect(charge_schedule.reload.invoiced_through).to eq(Date.new(2023, 4, 30))

    # Already billed
    result = described_class.call(charge_schedule.reload)
    expect(result.invoices.size).to eq(0)
  end

  # No last month's rent billing
  it 'does not bill last months rent during the month' do
    charge_schedule = create(:charge_schedule)

    lease = charge_schedule.chargeable

    configure_rent_schedule(lease.configuration)

    lease.update!(
      start_date: Date.new(2023, 3, 1),
      end_date: Date.new(2023, 3, 31)
    )

    create(:charge_schedule_entry,
           lease: lease,
           charge_schedule: charge_schedule,
           amount: '$100.00',
           start_date: lease.start_date,
           end_date: lease.end_date)

    result = described_class.call(charge_schedule.reload)
    expect(result.invoices.size).to eq(0)

    # Charge schedule invoiced through should not have updated
    expect(charge_schedule.reload.invoiced_through).to be_nil
  end

  # April billing when month to month
  it 'does not bill last months rent during the month' do
    charge_schedule = create(:charge_schedule)

    lease = charge_schedule.chargeable

    configure_rent_schedule(lease.configuration)

    lease.update!(
      kind: :rollover,
      start_date: Date.new(2023, 3, 1),
      end_date: Date.new(2023, 3, 31)
    )

    create(:charge_schedule_entry,
           lease: lease,
           charge_schedule: charge_schedule,
           amount: '$100.00',
           start_date: lease.start_date,
           end_date: lease.end_date,
           inherit_term: true)

    result = described_class.call(charge_schedule.reload)
    expect(result.invoices.size).to eq(1)

    # April entry should have billed, with month to month charge
    invoice = result.invoices.first
    expect(invoice.amount.format).to eq('$125.00')
    expect(invoice.description).to eq('April Rent')

    # Charge schedule should be billed through April
    expect(charge_schedule.reload.invoiced_through).to eq(Date.new(2023, 4, 30))
  end
end
