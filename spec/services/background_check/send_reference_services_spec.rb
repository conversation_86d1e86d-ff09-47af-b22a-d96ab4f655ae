require 'rails_helper'

RSpec.describe BackgroundCheck::SendReferenceServices do
  let(:xml) { 'the_xml' }

  it 'handles success', vcr: {
    cassette_name: 'send_reference_services',
    match_requests_on: %i[uri method body headers]
  } do
    result = described_class.new(xml).call
    expect(result).to be_successful
  end

  it 'handles failure', vcr: {
    cassette_name: 'send_reference_services_unsuccessfully',
    match_requests_on: %i[uri method body headers]
  } do
    result = described_class.new(xml).call
    expect(result).not_to be_successful
  end
end
