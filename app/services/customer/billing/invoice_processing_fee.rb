class Customer::Billing::InvoiceProcessingFee < Customer::Billing::Factor
  def line_items
    line_item(
      "Invoice Processing Above #{invoice_processing_allotted} Allotted",
      invoice_processing_fee,
      billable_quantity
    )
  end

  def billable_quantity
    return 0 if invoice_processing_allotted.nil?

    total_processed - invoice_processing_allotted
  end

  def total_processed
    invoices = InvoiceProcessing::Email.completed.where(
      processed_at: billing_period
    )

    if @pricing_plan.multi_invoice?
      invoices = invoices.joins(:invoice).where(
        invoices: { buyer: [@company, @company.properties].flatten }
      )
    end

    invoices.count
  end

  delegate :invoice_processing_allotted, :invoice_processing_fee,
           to: :@pricing_plan
end
