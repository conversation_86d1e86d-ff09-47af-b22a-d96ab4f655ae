##
# A collection of {BudgetAmount}s for various {Plutus::Account}s.
# Used for comparison with actual account balances.
class Budget < ApplicationRecord
  belongs_to :company, optional: false
  belongs_to :property, optional: true
  has_many :budget_amounts, dependent: :destroy,
                            class_name: 'BudgetAmount::Monthly'

  validates :year, presence: true, numericality: true

  def name
    [
      company.name,
      property&.name,
      year
    ].compact.join(' / ')
  end

  def url
    accounting_budget_path(self)
  end

  def account_link(account)
    routes.accounting_journal_account_path(company, account)
  end

  def fiscal_year
    company.fiscal_year(year)
  end
end
