module Importers::V3::Base::RowLimit
  extend ActiveSupport::Concern

  DEFAULT_MAXIMUM_ROW_LIMIT = 2500

  included do
    set_callback :validate, :before, :limit_row_count
  end

  private

  def limit_row_count
    count = parser.row_count

    logger.debug { "Row count: #{count}." }

    return if count <= max_row_limit

    fail Importers::V3::Errors::RowLimitExceededError.new count:, maximum: max_row_limit
  end

  def max_row_limit
    DEFAULT_MAXIMUM_ROW_LIMIT
  end
end
