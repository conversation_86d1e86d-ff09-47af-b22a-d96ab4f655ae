class Organization::Forms::Fields<PERSON>ontroller < ApplicationController
  before_action :check_feature_flag

  def update
    @field = CustomForms::FormField.find(params[:id])
    @field.element.update!(params[:element].permit(@field.element.permitted_keys))
    redirect_to edit_organization_form_path(@field.form)
  rescue ActiveRecord::RecordInvalid => _e
    render @field.editor_component, status: :unprocessable_entity, content_type: 'text/html'
  end

  private

  def check_feature_flag
    return if Feature.enabled?(:custom_forms, Customer.current)

    redirect_to manage_companies_path, alert: 'Not Enabled' and return
  end
end
