class Reports::V3::ComparativeCashFlowStatement < Reports::V3::Comparative::Report
  include Reports::V3::Comparative::YearToDate

  def self.available_filters
    super.without(:basis) # Cash flow statements are implied to be for accrual journals
  end

  def tree_builder
    @tree_builder ||= ChartOfAccounts::TreeBuilder::CashFlowStatement.new(
      accounting_context: accounting_context,
      colspan: tree_builder_colspan,
      report: self
    )
  end

  def net_income_values
    comparative_subjects.map do |subject|
      subject_accounting_context = subject.instance_variable_get(:@accounting_context)
      subject_filters = subject.instance_variable_get(:@filters)
      accounts = subject_accounting_context.accounts

      filters = {
        **subject_accounting_context.query_params,
        from_date: subject_filters[:start_date],
        to_date: subject_filters[:end_date],
        skip_equity_transfers: true
      }.compact_blank

      Accounting::IncomeStatement.new(accounts, filters).net_income
    end
  end

  def account_balance(account, subject)
    value = subject.balance(account)

    if tree_builder.send(:operating_activities?, account)
      if account.is_a?(Plutus::Asset)
        -value
      else
        value
      end
    elsif tree_builder.send(:investing_activities?, account)
      -value
    elsif tree_builder.send(:financing_activities?, account)
      if account.contra?
        -value
      else
        value
      end
    else
      fail NotImplementedError
    end
  end
end
