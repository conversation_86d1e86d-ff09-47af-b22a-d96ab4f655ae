require_relative 'seed_helpers'

SeedContext.seed do
  subdomain = ENV.fetch('seed_subdomain', 'alever')
  customer_kind = ENV.fetch('seed_customer_kind', 'owner_operator') # or, e.g., 'greek_housing'

  check_for_existing_customer_and_destroy_or_bail!(subdomain)

  seed_puts "Creating sample '#{subdomain}' #{customer_kind} customer."

  # Set this to false to go through customer setup
  already_setup = true

  admin_username = default_username || 'admin'
  admin_email = "#{admin_username}@revela.co"
  employee_username = default_username || 'test'
  employee_email = "#{employee_username}@revela.co"

  ActiveRecord::Base.transaction do
    admin = AdminUser.create_with(password: 'TrustNo1!', top_level: true)
                     .find_or_create_by!(email: admin_email)
    log_record(admin, description: "Admin (#{admin.email})", path: '/admin')

    account_params = ActionController::Parameters.new(
      account: {
        first_name: 'Test',
        last_name: 'User',
        email: employee_email,
        password: 'TrustNo1!',
        password_confirmation: 'TrustNo1!'
      }
    )

    registration = Customer::Registration.create!(
      first_name: '<PERSON><PERSON>',
      last_name: 'Support',
      email: '<EMAIL>',
      phone: '************',
      name: 'Alever Property Management',
      subdomain: subdomain,
      unit_count: 150,
      website: "https://#{subdomain}.revela.co",
      management_style: customer_kind,
      setup_style: 'revela_managed'
    )

    result = Customer::Register.call(registration: registration,
                                     account_params: account_params)

    customer = result.customer

    customer.create_brand!(primary_color: '#F05A2A',
                           secondary_color: '#E8E2CE',
                           logo_url: 'https://i.imgur.com/ua5FxgC.png')
    customer.activate!

    Customer.current.setup_completed! if already_setup

    # 'Membership' Simple Agreement Type
    create(:agreement_type, name: 'Membership', slug: 'membership')

    # Add documents available as agreement templates
    %i[lease_template membership_template].each do |template_type|
      create(
        :document,
        template_type,
        upload_file_name: "sample_#{template_type}.docx",
        direct_upload_url: 'https://revela-public.s3.us-east-1.amazonaws.com/templates/sample_document_template.docx'
      )
    end

    chart_of_accounts = ChartOfAccounts.first!

    late_fees = create(:revenue_account, name: 'Late Fees',
                                         tenant: chart_of_accounts)

    configuration = Configuration.first!

    configuration.update!(display_unavailable_listings: true)

    client_entity = create(:company,
                           :client_entity,
                           name: 'Management, LLC',
                           chart_of_accounts: chart_of_accounts)
    log_record(client_entity, description: 'Client Entity')
    customer.update!(client_entity: client_entity)

    client_entity_bank_account = create(:bank_account,
                                        owner: client_entity,
                                        name: 'Trust Account')

    manager = PropertyManager.last!
    log_record(manager, description: "Employee (#{manager.email})")

    PropertyManager.find_each(&:confirm)

    portfolio = create(:portfolio,
                       configuration: configuration,
                       name: 'Sample Portfolio')

    company = create(:company, name: 'Sample, LLC', portfolio: portfolio)
    log_record(company)

    bank_account = create(:bank_account, owner: company, name: 'Rent Income')
    log_record(bank_account)

    # create a second bank account to set up some transfers later
    second_bank_account = create(:bank_account, owner: company, name: 'Operating')
    log_record(second_bank_account)

    # Make Operating Bank Account a Unit Bank Account
    create(:tunisia_deposit_account, bank_account: second_bank_account)
    3.times { create(:accounting_debit_card_purchase, bank_account: second_bank_account) }

    owner = create(:owner, email: '<EMAIL>',
                           password: 'TrustNo1!',
                           password_confirmation: 'TrustNo1!',
                           confirmed_at: Time.zone.now)
    log_record(owner, description: "Owner (#{owner.email})")

    create(:full_ownership, owner: owner, entity: company)

    property = create(:property, name: 'Sample Property',
                                 company: company,
                                 kind: :multifamily)
    log_record(property)

    contract = create(:management_contract, company: company)
    contract.memberships.create!(property: property)
    contract.account_settings.create!(ratio: 1.0, account: late_fees)

    create(:hap_contract, property: property)

    floorplan = create(:floorplan, name: 'Floorplan A',
                                   property: property)

    unit = create(:unit, property: property,
                         floorplan: floorplan,
                         name: 'Unit 1A')
    log_record(unit)

    listing = create(:listing, :published, floorplan: floorplan)
    sample_image_path = Rails.public_path.join('images/sticky.jpg')

    unless Rails.env.test?
      listing.shrine_photos.create!(upload: File.open(sample_image_path))
    end

    log_record(listing, path: routes.listing_path(listing))

    tenant = create(:tenant, first_name: 'Sample',
                             last_name: 'Tenant',
                             email: '<EMAIL>',
                             kind: :resident,
                             password: 'TrustNo1!',
                             password_confirmation: 'TrustNo1!',
                             confirmed_at: Time.zone.now)
    log_record(tenant, description: "Tenant (#{tenant.email})")

    unsubmitted_application = create(:lease_application,
                                     :completed,
                                     tenants: [tenant],
                                     unit: unit)
    log_record(unsubmitted_application,
               description: 'Unsubmitted Application',
               path: routes.hosted_application_path(unsubmitted_application.uuid))

    submitted_application = create(:lease_application,
                                   :screening_initiated,
                                   tenants: [tenant],
                                   unit: unit)
    application_membership = submitted_application
                             .lease_application_memberships
                             .first
    create(:background_check,
           :completed,
           lease_application_membership: application_membership)
    create(:the_closing_docs_screening_group,
           :completed,
           lease_application: submitted_application)
    create(:saferent_screening,
           :default,
           lease_application: submitted_application)
    create(:landlord_verification,
           :submitted,
           lease_application: submitted_application)
    log_record(submitted_application, description: 'Submitted Application')

    membership = build(:lease_membership,
                       tenant: tenant, move_in_date: 6.months.ago,
                       move_out_date: 6.months.from_now)

    lease = create(:lease,
                   unit: unit, start_date: 6.months.ago,
                   end_date: 6.months.from_now,
                   lease_memberships: [membership])
    log_record(lease)

    rent_invoice = create(:rent_invoice, :paid, membership: membership)
    log_record(rent_invoice, description: 'Rent Invoice')

    rent_payment = rent_invoice.payments.first!
    rent_payment.update!(deposit_bank_account: bank_account)
    log_record(rent_payment, description: 'Rent Payment')

    maintenance_ticket = create(:maintenance_ticket,
                                property: property,
                                unit: unit,
                                opened_by: tenant)
    log_record(maintenance_ticket)

    inspection_template = create(:inspection_template, :with_questions)

    inspection_report = create(:inspection_report,
                               :completed,
                               template: inspection_template,
                               property: property,
                               assigned_to: tenant)

    estimate = create(:maintenance_estimate,
                      inspection: inspection_report,
                      prepared_by: manager)
    log_record(estimate, path: routes.maintenance_estimate_path(estimate))

    vendor = create(:vendor)
    log_record(vendor)

    bid_request = create(:maintenance_bid_request,
                         maintenance_ticket: maintenance_ticket,
                         requested_by: manager,
                         message: 'Please submit a bid')
    bid_request_invite = bid_request.invites.create!(vendor:)
    log_record(bid_request,
               path: routes.vendor_bid_request_path(bid_request_invite.token))

    assignment = create(:vendor_assignment,
                        maintenance_ticket: maintenance_ticket,
                        vendor: vendor,
                        created_by: manager,
                        message: 'Please assist with this ticket')
    log_record(assignment,
               path: routes.vendor_assignment_path(assignment.token))

    project = create(:project, name: 'Rehab Project')
    log_record(project)

    invoice = create(:vendor_invoice, seller: vendor, buyer: property)
    log_record(invoice)

    maintenance_ticket.linked_invoices << invoice
    project.linked_invoices << invoice

    batch = create(:payment_batch)
    batch.add_invoice!(invoice)
    log_record(batch)

    # Merchant Accounts
    [bank_account, client_entity_bank_account].each do |account|
      create(:merchant_account,
             :zeamster,
             :ach,
             :credit_card,
             ach_debit_markup: '$0.75',
             credit_card_rate: 0.03,
             bank_account: account)
    end

    # Owner statement activity
    due_from = company.chart_of_accounts.due_from_client_entity_account
    create(:journal_entry,
           amount: '$100.00',
           description: 'Sample Cash In',
           journal: company,
           property: property,
           debit_account: due_from)

    # Payment Plan Eligibility
    Invoice.where(buyer_type: 'Tenant').update_all(payment_plan_eligible: true)

    # Owner Contribution Request
    contribution_request = create(:owner_contribution_request,
                                  owner: owner,
                                  property: property,
                                  amount: '$100.00',
                                  description: Faker::Lorem.sentence,
                                  message: Faker::Lorem.paragraph)
    log_record(
      contribution_request,
      path: routes.owners_contribution_request_path(contribution_request)
    )

    # Tenant Payment Plan
    payment_method = create(
      :credit_card,
      merchant_account: bank_account.merchant_accounts.first,
      owner: tenant
    )
    payment_plan = create(:payment_plan,
                          :with_invoices,
                          payment_method: payment_method,
                          lease_membership: membership)
    path = routes.tenant_payment_plan_path(tenant, payment_plan)
    log_record(payment_plan, path: path)

    api_v2_token = 'abcdefghijklmnopqrstuvwxyz123456789'
    api_v2_key = create(:api_v2_key,
                        customer:,
                        token: api_v2_token,
                        user: manager)

    seed_print 'API V2 Token'.ljust(32)
    seed_puts api_v2_token

    if customer.greek_housing?
      # Member onboarding
      membership_agreement = build(
        :member_onboarding_membership_agreement,
        membership_template: Document.template_type('agreement_template').first!,
      )

      lease_agreement = build(
        :member_onboarding_lease_agreement,
        lease_template: Document.template_type('lease').first!,
      )

      member_onboarding = create(
        :member_onboarding_configuration,
        :information_collection,
        :guarantor,
        :charge,
        membership_agreement:,
        lease_agreement:,
        num_property_memberships: 0
      )

      create(
        :member_onboarding_property_membership,
        configuration: member_onboarding,
        property:,
        enhanced: true
      )

      create(
        :member_onboarding_assignment,
        configuration: member_onboarding,
        tenant:
      )
    end
  end
end
