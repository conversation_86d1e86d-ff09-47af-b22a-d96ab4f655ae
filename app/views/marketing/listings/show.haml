= render ActionSidebar::ContainerComponent.new do |c|
  - c.with_main do
    = render 'flash_notices'

    .ui.two.column.grid
      .column
        .ui.breadcrumb
          = link_to 'Listings', marketing_listings_path, class: 'section'
          %i.right.angle.divider.icon
          %h1.ui.header
            = @listing.name
            .sub.header
              - if @listing.published?
                .ui.green.mini.empty.circular.label
                Published Listing
              - else
                .ui.red.mini.empty.circular.label
                Unpublished Listing
              for
              = link_to @listing.property.name, property_path(@listing.property)

      .column
        = link_to edit_marketing_listing_path(@listing),
          class: 'right floated ui button' do
          %i.edit.outline.icon
          Edit

        = link_to marketing_listing_path(@listing),
          method: :delete,
          data: { confirm: 'Delete this listing?' },
          class: 'right floated basic ui icon button' do
          %i.trash.alternate.outline.icon

        - if @listing.published?
          = link_to unpublish_marketing_listing_path(@listing),
            method: :patch,
            class: 'right floated basic ui button' do
            %i.eye.slash.outline.icon
            Unpublish

        - else
          = link_to publish_marketing_listing_path(@listing),
            method: :patch,
            class: 'right floated basic ui button' do
            %i.eye.icon
            Publish

        = link_to listing_path(@listing),
          target: '_blank',
          class: 'right floated ui basic button' do
          %i.external.link.icon
          - if @listing.published?
            View
          - else
            Preview
          Listing

    %h4.ui.header
      Description
    = auto_link simple_format(@listing.description)

    %h4.ui.header
      Amenities
    - if @listing.amenities.any?
      .ui.bulleted.list
        - @listing.amenities.each do |amenity|
          .item= amenity
    - else
      %i None

    %h4.ui.header
      Photos
    - if @listing.shrine_photos.any?
      .ui.small.rounded.images.image-gallery-container#sorted-content{ style: 'display: flex; flex-direction: row; flex-wrap: wrap;', data: { controller: 'sortable-images' } }
        - @listing.shrine_photos.each do |photo|
          = link_to photo.upload_url, data: { id: photo.id } do
            = image_tag photo.upload_url(:small),
              class: 'ui small rounded image',
              style: 'width: 120px; height: 120px; object-fit: cover;'
    - else
      %i None

    %h4.ui.header
      Notes
    - if @listing.notes.present?
      = simple_format @listing.notes
    - else
      %i None

    = render partial: 'management/audit_logs/loader',
      locals: { auditable: @listing }

  - c.with_sidebar do |s|
    = render partial: 'sidebar', locals: { s: s, listing: @listing.decorate }

:javascript
  $('.image-gallery-container').magnificPopup({
    type: 'image',
    delegate: 'a',
    gallery: { enabled: true }
  });

:css
  .draggable--over {
    opacity: 25%;
  }
