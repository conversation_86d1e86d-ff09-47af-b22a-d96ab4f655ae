class SplitVerificationDocumentsRequirements < ActiveRecord::Migration[6.1]
  def change
    rename_column :configurations,
                  :verification_document_requirements,
                  :financially_responsible_document_requirements

    add_column :configurations,
               :other_adult_occupant_document_requirements,
               :text

    reversible do |direction|
      direction.up do
        Configuration.find_each do |configuration|
          requirements = \
            configuration.financially_responsible_document_requirements

          configuration.update_columns(
            other_adult_occupant_document_requirements: requirements
          )
        end
      end
    end
  end
end
