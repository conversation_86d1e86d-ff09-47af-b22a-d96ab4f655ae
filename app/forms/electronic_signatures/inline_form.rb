class ElectronicSignatures::InlineForm
  include ActiveModel::Model

  attr_reader :post_path, :hosted_document_path, :signature_parameters

  def initialize(post_path:, hosted_document_path:, signature_parameters: nil)
    @post_path = post_path
    @hosted_document_path = hosted_document_path
    @signature_parameters = OpenStruct.new(signature_parameters || {})
  end

  delegate(*ElectronicSignature::Parameters::ALL_FIELDS, to: :signature_parameters, allow_nil: true)

  def requested_by
    if Customer.current_subdomain.in?(%w[ghm ghm-sandbox sae sae-sandbox])
      OpenStruct.new(
        name: 'Greek Housing Management',
        email: '<EMAIL>'
      )
    else
      Customer.current.representative
    end
  end

  def document
    nil
  end

  def signed?
    false
  end

  def expired?
    false
  end

  def needs_ach_information?
    false
  end
end
