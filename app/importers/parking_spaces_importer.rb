class ParkingSpacesImporter < Importers::Importer::V2
  include Importers::XLSX

  attr_reader :parking_lot

  def initialize(path:, parking_lot:)
    super(path: path)
    @parking_lot = parking_lot
  end

  protected

  def results_key
    :parking_spaces
  end

  private

  def import_row(row)
    name = row.value('Name', 'Space', 'Parking Space').presence
    price = row.value('Price', 'Rate', 'Rent', 'Rental Rate').presence

    parking_lot.parking_spaces.find_or_initialize_by(name: name).tap do |space|
      space.price = Monetize.parse(price)
      space.save!
    end
  end
end
