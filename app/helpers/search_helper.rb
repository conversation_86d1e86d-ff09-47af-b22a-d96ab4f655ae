module SearchHelper
  # Renders a search result partial
  # e.g. for an instance of Tenant, effectively:
  #
  #   render partial: 'search/tenant', locals: { tenant: result }
  #
  def render_search_result_partial(result)
    name = result.class.searchable_type

    render partial: "search/#{name}",
           locals: { name.to_sym => result }
  end

  def render_card_item_partial(item)
    class_name = item.class.to_s
    path = "management/#{class_name.pluralize.underscore}/card"
    render partial: path, locals: { class_name.underscore.to_sym => item }
  end
end
