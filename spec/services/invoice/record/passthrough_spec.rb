require 'rails_helper'

RSpec.describe 'recording passthrough invoice revenue' do
  let!(:client_entity) { Customer.current.client_entity }

  let(:chart_of_accounts) { client_entity.chart_of_accounts }

  let!(:property) do
    create(:property).tap do |property|
      property.configuration.update!(chart_of_accounts: chart_of_accounts)
    end
  end

  let(:accounts_receivable) { chart_of_accounts.accounts_receivable }

  let!(:admin_fee) do
    create(:revenue_account, tenant: chart_of_accounts, passthrough: true)
  end

  let!(:rent_income) do
    create(:revenue_account, tenant: chart_of_accounts).tap do |account|
      property.configuration.create_rent_preset!(
        account: account, amount: Money.zero, name: 'Rent'
      )
    end
  end

  let!(:due_from_client_entity) do
    chart_of_accounts.due_from_client_entity_account
  end

  let!(:due_to_customer) do
    chart_of_accounts.due_to_customer_account
  end

  let(:invoice) do
    unit = create(:unit, property: property)
    membership = create(:lease_membership, unit: unit)

    invoice = build(:rent_invoice, membership: membership, amount: '$100.00')

    invoice.line_items.build(
      unit_price: '$50.00',
      quantity: 1,
      description: 'Admin Fee',
      receivable_account: admin_fee
    )

    invoice.save!

    invoice
  end

  before do
    create(:management_contract, company: property.company).tap do |contract|
      create(:management_contract_membership,
             property: property,
             management_contract: contract)

      create(:management_contract_account_setting,
             management_contract: contract,
             account: admin_fee,
             ratio: 0.5)
    end
  end

  it 'records a passthrough entry on the management company journal' do
    entry = invoice.journal_entries.accrual_basis_passthrough.first!

    expect(entry.journal).to eq(client_entity)
    expect(entry.date).to eq(invoice.post_date)
    expect(entry.description).to eq(invoice.description)
    expect(entry.amounts.count).to eq(2)

    # Debits
    debit = entry.debit_amounts.first!
    expect(debit.account).to eq(due_to_customer)
    expect(debit.amount).to eq(25_00)

    # Credits
    credit = entry.credit_amounts.first!
    expect(credit.account).to eq(admin_fee)
    expect(credit.amount).to eq(25_00)
  end

  it 'records due to for the passthrough revenue' do
    entry = invoice.journal_entries.accrual_basis_invoice_post.first!

    expect(entry.journal).to eq(property.company)
    expect(entry.date).to eq(invoice.post_date)
    expect(entry.description).to eq(invoice.description)
    expect(entry.amounts.count).to eq(4)

    # Debits
    debit = entry.debit_amounts.first!
    expect(debit.account).to eq(accounts_receivable)
    expect(debit.amount).to eq(150_00)

    # Credits
    credit = entry.credit_amounts.find_by!(account: rent_income)
    expect(credit.amount).to eq(100_00)

    credit = entry.credit_amounts.find_by!(account: admin_fee)
    expect(credit.amount).to eq(25_00)

    credit = entry.credit_amounts.find_by!(account: due_from_client_entity)
    expect(credit.amount).to eq(25_00)
  end
end
