WITH

-- CTE to allow configuring an effective date other than current_date
effective_date AS (
  SELECT COALESCE(
    NULLIF(
      current_setting('reporting_settings.effective_date', TRUE),
      ''
    ),
    current_date::text
  )::date AS date
),

rent_accounts AS (
  SELECT
    plutus_accounts.id AS account_id
  FROM
    plutus_accounts
  WHERE
    plutus_accounts.category = 'Rent Income'
      OR
        plutus_accounts.id IN (
          SELECT account_id FROM charge_presets WHERE charge_presets.kind = 5 -- Rent Kind
        )
),

assistance_rent_accounts AS (
  SELECT
    plutus_accounts.id AS account_id
  FROM
    plutus_accounts
  WHERE
    plutus_accounts.category = 'Rent Assistance Income'
),

late_fee_accounts AS (
  SELECT charge_presets.account_id AS account_id FROM charge_presets WHERE charge_presets.kind = 4 -- Late Fee Kind
),

late_invoices AS (
  SELECT
    invoices.id AS invoice_id,
    invoices.buyer_lease_membership_id,
    invoices.post_date,
    invoices.due_date
  FROM
    invoices
  LEFT JOIN
    effective_date ON TRUE
  WHERE
    (invoices.due_date < effective_date.date)
      AND
        (invoices.waived_at IS NULL OR invoices.waived_at > effective_date.date)
)

SELECT
  lease_chain_id AS id,
  lease_chain_id,
  oldest_overdue_post_date,
  oldest_overdue_due_date,
  overdue_balance_cents,
  overdue_rent_cents,
  overdue_assistance_rent_cents,
  overdue_balance_cents - overdue_assistance_rent_cents AS overdue_resident_balance_cents,
  overdue_late_fees_cents,
  overdue_balance_cents - overdue_rent_cents - overdue_assistance_rent_cents - overdue_late_fees_cents AS overdue_other_cents
FROM (
  SELECT
    lease_chains.id AS lease_chain_id,
    MIN (late_invoices.post_date) AS oldest_overdue_post_date,
    MIN (late_invoices.due_date) AS oldest_overdue_due_date,
    COALESCE (
      SUM (
        line_item_receivable_balances.balance_cents
      ),
      0
    ) AS overdue_balance_cents,
    COALESCE (
      SUM (
        CASE WHEN line_items.receivable_account_id IN (SELECT account_id FROM rent_accounts) THEN line_item_receivable_balances.balance_cents ELSE 0 END
      ),
      0
    ) AS overdue_rent_cents,
    COALESCE (
      SUM (
        CASE WHEN line_items.receivable_account_id IN (SELECT account_id FROM assistance_rent_accounts) THEN line_item_receivable_balances.balance_cents ELSE 0 END
      ),
      0
    ) AS overdue_assistance_rent_cents,
    COALESCE (
      SUM (
        CASE WHEN line_items.receivable_account_id IN (SELECT account_id FROM late_fee_accounts) THEN line_item_receivable_balances.balance_cents ELSE 0 END
      ),
      0
    ) AS overdue_late_fees_cents
  FROM
    lease_chains
  LEFT JOIN
    late_invoices
      ON
        late_invoices.buyer_lease_membership_id = ANY(lease_chains.lease_membership_ids)
  LEFT JOIN
    line_items
      ON
        line_items.invoice_id = late_invoices.invoice_id
  LEFT JOIN
    line_item_receivable_balances
      ON
        line_item_receivable_balances.line_item_id = line_items.id
  GROUP BY
    lease_chains.id
) subquery;
