class Reports::V3::Tours < Reports::V3::Report
  def self.available_filters
    %i[property date_range]
  end

  def self.default_filter_values
    {
      start_date: 1.week.ago,
      end_date: 1.week.from_now
    }
  end

  def mapping
    {
      date: 'Date',
      time: 'Time',
      property: 'Property',
      tour_guide: 'Tour Guide',
      name: 'Name',
      email: 'Em<PERSON>',
      phone: 'Phone',
      message: 'Message'
    }
  end

  def scope
    Reports::V3::Scope.new(user: user, filters: filters)
  end

  private

  def basis
    Reports::V3::Basis::Tour
  end
end
