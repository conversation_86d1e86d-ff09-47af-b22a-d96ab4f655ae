WITH

-- CTE to allow configuring an effective date other than current_date
-- Copied from lease_membership_aging_delinquencies_v06.sql
effective_date AS (
  SELECT COALESCE(
    NULLIF(
      current_setting('reporting_settings.effective_date', TRUE),
      ''
    ),
    current_date::text
  )::date AS date
),

-- Copied from invoice_total_payments view
-- Copied from lease_membership_aging_delinquencies_v06.sql
effective_invoice_total_payments AS (
  SELECT
    invoices.id AS invoice_id,
    COALESCE(
      SUM(
        CASE
        WHEN (reversed_at IS NULL OR reversed_at > effective_date.date) THEN invoice_payments.amount_cents
        ELSE 0
        END
      ),
      0
    ) AS payment
  FROM
    invoices
  LEFT JOIN
    effective_date ON TRUE
  LEFT JOIN
    invoice_payments
  ON
    invoice_payments.invoice_id = invoices.id
    AND
    invoice_payments.date <= effective_date.date
  GROUP BY invoices.id
),

-- Primary rent is 1,
-- Secondary rent is 2,
-- Other is 3
account_priority AS (
  SELECT
    plutus_accounts.id AS account_id,
    (
      CASE
      WHEN plutus_accounts.id IN (
        SELECT account_id FROM charge_presets WHERE kind = 5 -- rent type
      )
      THEN
        1 -- Primary Rent
      ELSE
        (
          CASE
          WHEN plutus_accounts.category = 'Rent'
          THEN
            2 -- Secondary Rent
          ELSE
            3 -- Not Primary or Secondary Rent
          END
        )
      END
    ) AS priority
  FROM
    plutus_accounts
)

SELECT
  -- Fake primary key
  line_items.id AS id,

  -- Line Item ID
  line_items.id AS line_item_id,

  -- Associated Invoice ID
  invoices.id AS invoice_id,

  -- Associated Receivable Plutus Account ID
  plutus_accounts.id AS account_id,

  -- Zero if waived invoice
  (
    CASE
    WHEN
      (invoices.waived_at IS NULL OR invoices.waived_at > effective_date.date)
    THEN
      (
        -- Item Amount
        (line_items.unit_price_cents * line_items.quantity)
        -- Less Applied Amount
        - (
          CASE
          WHEN
            (
              -- The effective invoice paid amount, or zero
              COALESCE(effective_invoice_total_payments.payment, 0)
              -- Less the running total of the ordered line items
              - SUM(line_items.unit_price_cents * line_items.quantity)
              OVER prioritized_invoice_line_items
            ) < 0
          THEN -- Negative, we overapplied
            GREATEST(
              -- The line item amount
              (line_items.unit_price_cents * line_items.quantity)
              -- Plus the (negative) overapply amount, is the difference or applied amount
              + (
                COALESCE(effective_invoice_total_payments.payment,0)
                - SUM(line_items.unit_price_cents * line_items.quantity)
                OVER prioritized_invoice_line_items
              ),
              0 -- But only if we have enough to partially apply
            )
          ELSE -- Postive, we completely apply
            line_items.unit_price_cents * line_items.quantity
          END
        )
      )
    ELSE
      0
    END
  ) AS balance_cents
FROM
  line_items
LEFT JOIN
  effective_date ON TRUE
JOIN
  invoices ON line_items.invoice_id = invoices.id
JOIN
  plutus_accounts ON line_items.receivable_account_id = plutus_accounts.id
JOIN
  account_priority ON account_priority.account_id = plutus_accounts.id
LEFT JOIN
  effective_invoice_total_payments
    ON effective_invoice_total_payments.invoice_id = invoices.id
WINDOW
  prioritized_invoice_line_items
    AS (
      PARTITION BY
        invoices.id
      ORDER BY
        -- Sort negative line items first so that they apply as credits to later items
        (CASE WHEN line_items.unit_price_cents * line_items.quantity < 0 THEN 0 ELSE 1 END) ASC,

        -- Next sort by account priority
        account_priority.priority ASC,

        -- then sort by GL code
        plutus_accounts.gl_code ASC,

        -- Finally sort by oldest first
        line_items.id ASC
      ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
    )
;
