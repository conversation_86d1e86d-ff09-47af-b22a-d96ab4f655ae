class Leasing::NoticesOfNonRenewalController < LeasingController
  before_action :set_lease

  def create
    ActiveRecord::Base.transaction do
      @lease.fixed!
      notice = @lease.build_notice_of_non_renewal(notice_params)
      notice.submitted_by = current_property_manager
      notice.save!

      tenant = @lease.primary_tenant
      tenant.update!(tenant_params)

      flash[:success] = 'Notice Of Non Renewal Recorded Successfully'
      redirect_to leasing_lease_path(@lease)
    end
  rescue ActiveRecord::RecordInvalid => e
    render_ujs_errors e.record.errors.full_messages
  end

  def destroy
    @lease.notice_of_non_renewal.destroy!

    flash[:success] = 'Notice Of Non Renewal Removed Successfully'
  rescue ActiveRecord::RecordNotDestroyed => e
    flash[:error] = e.record.errors.full_messages
  ensure
    redirect_to leasing_lease_path(@lease)
  end

  private

  def set_lease
    @lease = Lease.find(params[:lease_id])
  end

  def notice_params
    params.require(:notice_of_non_renewal)
          .permit(:date, :anticipated_move_out_date, :reason)
  end

  def tenant_params
    address_params =
      params
      .require(:notice_of_non_renewal)
      .permit(forwarding_address: %i[line_one line_two city region postal_code])

    {
      forwarding_address_attributes: address_params[:forwarding_address]
    }
  end
end
