class Tenants::Dashboard::PaymentPlansItemComponent < ApplicationComponent
  def initialize(tenant:)
    super()
    @tenant = tenant
  end

  def render?
    Feature.enabled?(:payment_plans, Customer.current)
  end

  private

  def already_has_payment_plan?
    current_or_upcoming_payment_plans.any?
  end

  def current_or_upcoming_payment_plans
    @current_or_upcoming_payment_plans ||=
      PaymentPlansQuery
      .new.search
      .by_tenant(@tenant)
      .with_current_or_upcoming_installments
  end

  def setup_text
    if already_has_payment_plan?
      'Setup Another Payment Plan'
    else
      'Setup A Payment Plan'
    end
  end

  def setup_path
    new_tenants_payment_plan_path
  end
end
