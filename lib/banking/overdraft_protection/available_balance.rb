class Banking::OverdraftProtection::AvailableBalance
  def initialize(bank_account:)
    @bank_account = bank_account
  end

  def balance
    return nil unless tunisia_account?

    (reported_available_balance - in_transit_amount).clamp(Money.zero..)
  end

  private

  attr_reader :bank_account

  delegate :tunisia_account?, :tunisia_deposit_account, to: :bank_account

  def reported_available_balance = tunisia_deposit_account.available

  def in_transit_amount = Money.new(in_transit_ach_credits.sum(:amount_cents))

  def in_transit_ach_credits
    in_transit_statuses = %i[pending_origination originating originated]

    Payment
      .joins(:zeamster_transaction)
      .where(withdrawal_bank_account: bank_account)
      .merge(ZeamsterTransaction.where(status: in_transit_statuses))
  end
end
