class ElectronicDocuments::MetadataFieldsComponent < ApplicationComponent
  def initialize(f:, document_templates:, remove_prefix: true)
    super()
    @f = f
    @document_templates = Array(document_templates)
    @remove_prefix = remove_prefix
  end

  def render?
    metadata_keys.any?
  end

  private

  attr_reader :f, :document_templates, :remove_prefix

  def metadata_keys
    @metadata_keys ||= begin
      tags = []

      document_templates.each do |document|
        docx_file = DocumentFilling::DocumentCache.fetch_or_download(
          document: document
        )

        tags += DocxFilling::ListTags.tags(docx_file)
      end

      tags.filter_map do |tag|
        tag =~ /meta(?:data)?\[(.*)\]/

        name = Regexp.last_match(1)

        next if name.blank?

        key = tag

        key = name if remove_prefix

        [key, name.titleize]
      end.sort.to_h
    end
  end
end
