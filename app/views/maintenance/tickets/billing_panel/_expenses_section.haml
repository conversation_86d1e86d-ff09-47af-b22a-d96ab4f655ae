- if panel.invoices.any?
  %table.ui.very.basic.compact.selectable.table
    %thead
      %tr
        %th/
        %th.center.aligned Item
        %th.right.aligned Amount
        %th.right.aligned Markup
        %th.right.aligned Sub Total
        %th/
    %tbody
      - if panel.billed_invoices.any?
        %tr
          %td/
          %td{ colspan: 5 }
            %b Already Billed
        - panel.billed_invoices.each do |invoice|
          %tr{ data: { invoice_id: invoice.id } }
            %td
            %td{ colspan: 5 }
              = link_to "#{invoice.seller.name} / #{invoice.invoice_number} - #{invoice.description}",
                invoice.url
          - panel.billed_line_items.select { |li| li.invoice == invoice }.each do |item|
            %tr
              %td/
              %td
                &nbsp;
                &nbsp;
                = item.description
              %td.collapsing.right.aligned
                = item.quantity
                ×
                &nbsp;
                = item.unit_price.format
              %td.right.aligned
                = item.sourcing_markup.format
              %td.right.aligned
                %span.subtotal
                  = item.sourcing_markup.final_amount.format
              %td.right.aligned
                - bill = item.sourcing_markup.destination_item.invoice
                = link_to bill.invoice_number,
                  accounting_receivables_invoice_path(bill)
        %tr
          %td.right.aligned{ colspan: 4 }
            %b Billed Expenses Total
          %td.right.aligned
            = panel.billed_expenses_total.format
          %td/

      %tr
        %td
          - if panel.invoices_not_billed.any?
            .ui.fitted.checkbox{ data: { maintenance_ticket__billing_panel_target: 'allInvoicesCheckbox' } }
              %input{ type: 'checkbox', checked: true }
              %label
        %td{ colspan: 4 }
          %b Need Billing
        %td/
      - panel.invoices_not_billed.each do |invoice|
        %tr{ data: { invoice_id: invoice.id } }
          %td.collapsing
            .ui.fitted.checkbox{ data: { maintenance_ticket__billing_panel_target: 'invoiceCheckbox' } }
              %input{ type: 'checkbox', checked: true }
              %label
          %td{ colspan: 6 }
            = link_to "#{invoice.seller.name} / #{invoice.invoice_number} - #{invoice.description}",
              invoice.url
        - invoice.line_items.each do |item|
          %tr{ data: { invoice_id: invoice.id, amount_cents: item.amount.cents, maintenance_ticket__billing_panel_target: 'itemRow' } }
            %td.collapsing
              .ui.fitted.checkbox{ data: { maintenance_ticket__billing_panel_target: 'itemCheckbox' } }
                = check_box_tag 'line_item_ids[]', item.id,
                  item.sourcing_markup ? !item.sourcing_markup.skip? : true
                %label
            %td
              &nbsp;
              &nbsp;
              = item.description
            %td.collapsing.right.aligned
              = item.quantity
              ×
              &nbsp;
              = item.unit_price.format
            %td.right.aligned
              .ui.fluid.action.input
                = text_field_tag "line_item_markup_values[#{item.id}]",
                  item.sourcing_markup ? item.sourcing_markup.markup_value : panel.default_materials_markup_percentage,
                  class: 'markup',
                  style: 'padding: 0 .5em;',
                  data: { action: 'keyup->maintenance-ticket--billing-panel#markupValueChanged' }

                %button.ui.small.compact.basic.icon.button{ type: 'button', data: { action: 'click->maintenance-ticket--billing-panel#markupTypeChanged' } }
                  - if item.sourcing_markup&.flat_markup&.present?
                    %i.small.dollar.sign.icon
                  - else
                    %i.small.percent.icon

                - if item.sourcing_markup&.flat_markup&.present?
                  = hidden_field_tag "line_item_markup_types[#{item.id}]", :fixed
                - else
                  = hidden_field_tag "line_item_markup_types[#{item.id}]", :percent
            %td.right.aligned
              %span.subtotal
                = item.amount.format
            %td/
      - if panel.invoices_not_billed.none?
        %tr
          %td{ colspan: 6 } None
      %tr
        %td.right.aligned{ colspan: 4 }
          %b Total Expense To Bill
        %td.right.aligned
          %b{ data: { maintenance_ticket__billing_panel_target: 'invoiceTotalAmount' } }
            $0.00
        %td/
      %tr
        %td.right.aligned{ colspan: 4 }
          %b Total Labor To Bill
        %td.right.aligned
          %b{ data: { maintenance_ticket__billing_panel_target: 'laborTotalAmount' } }
            $0.00
        %td/
      %tr
        %td.right.aligned{ colspan: 4 }
          %b Total To Bill
        %td.right.aligned
          %b{ data: { maintenance_ticket__billing_panel_target: 'totalAmount' } }
            $0.00
        %td/

- if panel.invoices.none?
  No invoices linked to ticket.
