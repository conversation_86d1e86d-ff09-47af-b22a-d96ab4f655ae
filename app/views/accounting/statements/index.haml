.action-bar
  .ui.container
    %h1.ui.header Statements

    .action.buttons
      %button.ui.button#prepare-statement-button
        %i.plus.icon
        Prepare Statement

.action-container{ style: 'padding-top: 2em;' }
  .ui.container
    %table.ui.very.basic.selectable.single.line.sortable.table
      %thead
        %tr
          %th Statement
          %th Entity
          %th Period Start
          %th Period End
          %th Prepared On
          %th Approved On
          %th Approved By
      %tbody
        - @statements.each do |statement|
          %tr
            %td= link_to statement.title, accounting_statement_path(statement)
            %td= statement.company.name
            %td= statement.start_date.to_datetime.to_fs(:short_date)
            %td= statement.end_date.to_datetime.to_fs(:short_date)
            %td= statement.created_at.to_fs(:short_date)
            %td= statement.approved_at&.to_fs(:short_date)
            %td
              - if statement.approved?
                = link_to statement.approved_by.name,
                  organization_employee_path(statement.approved_by)

.ui.small.modal#prepare-statement-modal
  %i.black.close.icon
  .header
    Prepare Statement
  .content
    = render partial: 'form'
  .actions
    %button.ui.cancel.button
      Cancel
    %button.ui.primary.approve.button{ type: 'submit',
    form: 'prepare_statement_form' }
      Submit

:javascript
  $('#prepare-statement-button').click(function () {
    $('#prepare-statement-modal')
      .modal(window.modalDefaults)
      .modal('setting', 'onApprove', false)
      .modal('setting', 'autofocus', false)
      .modal('show');
  });

  $('.ui.sortable.table').tablesort();
