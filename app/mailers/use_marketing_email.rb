module UseMarketingEmail
  extend ActiveSupport::Concern

  included do
    # TODO: After Rails 7.1, this can likely become before_deliver.
    after_action :set_marketing_delivery_options
  end

  private

  def set_marketing_delivery_options
    return unless Rails.env.production?

    mail.delivery_method.settings.merge!(
      user_name: ENV.fetch('MARKETING_MAIL_USER'),
      password: ENV.fetch('MARKETING_MAIL_PASSWORD'),
      address: ENV.fetch('MARKETING_MAIL_ADDRESS'),
      domain: ENV.fetch('MARKETING_MAIL_DOMAIN'),
      port: ENV.fetch('MARKETING_MAIL_PORT'),
      authentication: ENV.fetch('MARKETING_MAIL_AUTHENTICATION')
    )
  end
end
