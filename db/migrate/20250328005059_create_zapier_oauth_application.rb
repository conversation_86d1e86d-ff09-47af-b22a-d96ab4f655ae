class CreateZapierOAuthApplication < ActiveRecord::Migration[7.0]
  def up
    return unless Apartment::Tenant.current == 'public'

    Doorkeeper::Application.create!(
      name: '<PERSON><PERSON><PERSON>',
      uid: 'zapier',
      secret: SecureRandom.hex(32),
      redirect_uri: 'https://zapier.com/dashboard/auth/oauth/return/App219424CLIAPI/', # From Zapier
      scopes: 'read write'
    )
  end

  def down
    return unless Apartment::Tenant.current == 'public'

    Doorkeeper::Application.find_by(uid: 'zapier')&.destroy
  end
end
