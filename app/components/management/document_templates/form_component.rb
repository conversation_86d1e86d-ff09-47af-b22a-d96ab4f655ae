class Management::DocumentTemplates::FormComponent < ApplicationComponent
  def initialize(resource:)
    super()
    @resource = resource
  end

  private

  attr_reader :resource

  alias contact resource

  delegate :email, to: :contact

  def url
    case resource
    when Tenant
      tenant_document_templates_path
    when Owner
      owner_document_templates_path
    else
      fail 'Unknown contact type'
    end
  end

  def human_contact_type
    contact.class.name.downcase
  end

  def template_options
    documents = Document.template_options(template_type: :letter)

    documents.map do |document|
      [document.filename, document.id]
    end
  end

  def selected_template
    return nil unless params[:template_id]

    Document.find(params[:template_id])
  end

  def additional_attachment_options
    return [] unless resource.is_a?(Tenant)

    [['Past Due Charges', 'past_due_charges']]
  end
end
