.nested-fields
  .five.fields
    .required.field
      = f.label :first_name
      = f.text_field :first_name

    .required.field
      = f.label :last_name
      = f.text_field :last_name

    .required.field
      = f.label :relation
      = f.text_field :relation

    .required.field
      = f.label :phone
      = f.text_field :phone

    - unless CustomerSpecific::Behavior.lease_application_skip_emergency_contact_email?
      .field
        = f.label :email
        = f.text_field :email

    = link_to_remove_association f,
      class: 'ui basic icon button',
      style: 'align-self: flex-end;' do
      %i.remove.icon
