require 'rails_helper'

RSpec.describe Configuration::RentSchedule do
  subject { described_class.new(configuration: configuration, month: month) }

  let(:month) { Date.new(2022, 1, 1) } # January 2022

  let(:configuration) { create(:configuration) }

  context 'with defaults' do
    # Prepost 0
    # Rent Due Day 1
    # Rent Preset Net D 5

    its(:post_date) { is_expected.to eq(Date.new(2021, 12, 27)) }

    its(:rent_date) { is_expected.to eq(Date.new(2021, 12, 27)) }

    its(:due_date) { is_expected.to eq(Date.new(2022, 1, 1)) }
  end

  context 'with a common first day post configuration' do
    let(:configuration) do
      create(:configuration,
             rent_due_day: 6,
             rent_pre_post: 0)
    end

    its(:post_date) { is_expected.to eq(Date.new(2022, 1, 1)) }

    its(:rent_date) { is_expected.to eq(Date.new(2022, 1, 1)) }

    its(:due_date) { is_expected.to eq(Date.new(2022, 1, 6)) }
  end

  context 'with a common prepost configuration' do
    let(:configuration) do
      create(:configuration,
             rent_due_day: 6,
             rent_pre_post: 5)
    end

    its(:post_date) { is_expected.to eq(Date.new(2021, 12, 27)) }

    its(:rent_date) { is_expected.to eq(Date.new(2022, 1, 1)) }

    its(:due_date) { is_expected.to eq(Date.new(2022, 1, 6)) }
  end

  context 'with a high due date' do
    let(:configuration) do
      create(:configuration,
             rent_due_day: 25,
             rent_pre_post: 0).tap do |configuration|
               configuration.update!(
                 rent_preset: create(:charge_preset,
                                     configuration: configuration,
                                     net_d: 0,
                                     kind: :rent)
               )
             end
    end

    its(:post_date) { is_expected.to eq(Date.new(2021, 12, 25)) }

    its(:rent_date) { is_expected.to eq(Date.new(2021, 12, 25)) }

    its(:due_date) { is_expected.to eq(Date.new(2021, 12, 25)) }
  end
end
