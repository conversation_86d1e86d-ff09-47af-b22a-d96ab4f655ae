- selectable_dropdown_stimulus = Shigeki.new('selectable-dropdown', view_context: self)

.ui.floating.dropdown.item.multiple-selection{ **options.deep_merge(data: selectable_dropdown_stimulus.data { controller.action_click('openDropdown') }) }
  .default.text
    = item[:default_text]
  .selected.count
    0
  %i.dropdown.icon
  .menu.transition
    - if item[:search]
      .ui.icon.search.input
        %i.search.icon
        %input{ type: 'text', placeholder: item.dig(:search, :placeholder) }
      .scrolling.menu
        .selected-items
        = render partial: 'action_index/dropdown_selection_menu_items', locals: { item: item }
    - else
      .selected-items
      = render partial: 'action_index/dropdown_selection_menu_items', locals: { item: item }
