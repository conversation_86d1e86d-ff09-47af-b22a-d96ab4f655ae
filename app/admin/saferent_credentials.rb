ActiveAdmin.register Saferent::Credentials do
  permit_params :target_type, :target_id,
                :user_account, :user_name, :user_password

  form do |f|
    f.semantic_errors

    f.object.target ||= Customer.current

    f.inputs do
      f.input :target_type
      f.input :target_id
      f.input :user_account
      f.input :user_name
      f.input :user_password
    end
    f.actions
  end
end
