WITH membership_amounts AS (
  SELECT
    lease_membership_id,
    sum(charge_amounts.amount_cents) AS amount_cents
  FROM
    charge_amounts
  JOIN
    charges
      ON charge_amounts.charge_id = charges.id
      AND charges.recurring = TRUE
  GROUP BY
    charge_amounts.lease_membership_id
)
SELECT
  leases.id AS lease_id,
  leases.unit_id AS unit_id,
  tenants.id AS tenant_id,
  tenants.first_name || ' ' || tenants.last_name AS tenant_name,
  min(leases.start_date) AS start_date,
  max(leases.end_date) AS end_date,
  max(membership_amounts.amount_cents) as amount_cents,
  sum(lease_membership_balances.amount_cents) AS balance_cents
FROM
  lease_memberships
LEFT JOIN
  leases
    ON
      lease_memberships.lease_id = leases.id
LEFT JOIN
  tenants
    ON
      lease_memberships.tenant_id = tenants.id
LEFT JOIN
  lease_membership_balances
    ON
      lease_membership_balances.lease_membership_id = lease_memberships.id
LEFT JOIN
  membership_amounts
    ON
      membership_amounts.lease_membership_id = lease_memberships.id
GROUP BY leases.id, unit_id, tenants.id
;
