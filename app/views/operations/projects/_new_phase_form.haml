%button.ui.basic.button#new-phase-button
  %i.plus.icon
  Add Phase

= form_for @project.phases.build,
  url: operations_project_phases_path(@project),
  html: { id: 'new-phase-form',
  style: 'display: none;',
  class: 'ui form' } do |f|
  .ui.action.input{ style: 'padding-right: 8em;' }
    = f.text_field :name, placeholder: 'Phase Name'
    %button.ui.basic.submit.button
      Save
    %button.ui.basic.icon.button#cancel-phase-button{ type: 'button' }
      %i.remove.icon

:javascript
  $('#new-phase-button').click(function() {
    $('#new-phase-button').hide();
    $('#new-phase-form').css('display', 'inline-block');
    $('#new-phase-form input').focus();
  });

  $('#cancel-phase-button').click(function() {
    $('#new-phase-form').hide();
    $('#new-phase-button').show();
  });

