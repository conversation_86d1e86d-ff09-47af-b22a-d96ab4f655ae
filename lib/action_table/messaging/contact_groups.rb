class ActionTable::Messaging::ContactGroups < ActionTable::Base
  def multi_selection?
    true
  end

  def columns
    [name, member_count, activity, export]
  end

  private

  def name
    {
      title: 'Group Name',
      value: ->(contact_group) { contact_group.name },
      link: lambda { |contact_group|
              messaging_contact_group_path(contact_group)
            },
      align: :left
    }
  end

  def member_count
    {
      title: 'Member Count',
      value: lambda { |contact_group|
               pluralize(contact_group.contacts_count, 'contact')
             },
      align: :left
    }
  end

  def activity
    {
      title: 'Activity',
      value: lambda { |contact_group|
               if contact_group.most_recent_activity
                 "#{contact_group.most_recent_activity.contact_method} sent: #{contact_group.most_recent_activity.updated_at.strftime('%m/%d/%Y')}"
               else
                 'None in the past 30 days'
               end
             },
      align: :left
    }
  end

  def export
    {
      value: ->(_) { 'Export' },
      link: lambda { |contact_group|
              messaging_contact_group_memberships_path(contact_group,
                                                       format: :xlsx)
            },
      align: :left
    }
  end
end
