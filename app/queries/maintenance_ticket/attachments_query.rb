class MaintenanceTicket::Attachments<PERSON><PERSON>y
  def initialize(relation)
    @relation = relation.extending(Scopes)
  end

  def search
    @relation
  end

  def self.for_maintenance_ticket(ticket)
    new(
      MaintenanceTicket::AttachmentProxy
      .for_maintenance_ticket(ticket)
      .includes(
        :direct_attachment,
        :comment_attachment,
        :regarding_message_attachment,
        :regarding_message_embedded_attachment
      )
    ).search
  end

  module Scopes
    include Queries::MaxPagination

    def filter_with(filters)
      return self if filters.blank?

      filter_search(filters[:search])
    end

    def filter_search(search)
      return self if search.blank?

      where('filename ILIKE ?', "%#{search}%")
    end

    def before(time)
      where('created_at < ?', time)
    end
  end
end
