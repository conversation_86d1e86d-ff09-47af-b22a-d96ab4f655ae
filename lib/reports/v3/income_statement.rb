class Reports::V3::IncomeStatement < Reports::V3::Report
  def self.available_filters
    [
      if Feature.enabled?(:reports_scope_portfolios, Customer.current)
        :portfolio_accounting_context
      else
        :accounting_context
      end,
      (:property_region if CustomerSpecific::Behavior.reports_add_property_region_filters?),
      (:owner if CustomerSpecific::Behavior.reports_add_owner_filters?),
      :date_range,
      :basis
    ].compact
  end

  def self.default_filter_values
    {
      start_date: Time.zone.today.beginning_of_year,
      end_date: Time.zone.today.end_of_month
    }
  end

  def scope
    # Parent class already uses @scope for now
    @_scope ||= \
      Reports::V3::Scope.new(user: user, filters: filters, fallback: :company)
  end

  def as_json(options = {})
    in_basis { super(options) }
  end

  def as_xlsx(options = {})
    in_basis { super(options) }
  end

  def in_basis(&)
    if filters.basis == 'cash'
      Plutus.in_cash_basis(journal_cache_hint: journal_cache_hint, &)
    else
      yield
    end
  end

  def journal_cache_hint
    scope.company || scope.companies
  end

  def tree_builder
    ChartOfAccounts::TreeBuilder::IncomeStatement.new(
      accounting_context: accounting_context
    )
  end

  def rows
    return [] unless target

    tree_builder.call do |account|
      account_row(account)
    end
  end

  def account_row(account)
    balance = account_balance(account)

    return nil if balance.zero?

    link = amount_link(account)

    [
      {
        value: balance,
        link: link
      }
    ]
  end

  def account_link_filters
    @account_link_filters ||= { basis: filters.basis.presence }.compact
  end

  def account_link(account)
    if (journal = accounting_context.try(:company))
      routes.accounting_journal_account_path(
        journal, account, filters: account_link_filters
      )
    else
      routes.organization_chart_of_accounts_account_path(
        accounting_context.chart_of_accounts,
        account,
        filters: account_link_filters
      )
    end
  end

  def amount_link_filters
    @amount_link_filters ||= {
      **account_link_filters,
      from_date: period_beginning,
      to_date: period_ending
    }.compact
  end

  def amount_link(account)
    # Nowhere to show portfolio amounts by date range
    return nil unless (journal = accounting_context.try(:company))

    routes.accounting_journal_account_path(
      journal, account, filters: amount_link_filters
    )
  end

  def period_beginning
    filters.start_date.to_date
  end

  def period_ending
    filters.end_date.to_date
  end

  def account_balance(account)
    if use_account_matrix?
      @matrix ||= Accounting::AccountBalanceMatrix.new(
        accounting_context: target.accounting_context,
        start_date: period_beginning,
        end_date: period_ending,
        entry_filter: \
        Accounting::FeeManagement::CustomerPortfolioEntryFilter.new(
          filters
        ).filter
      )

      balance = @matrix.balance(account: account)

      account.contra? ? -balance : balance
    else
      balance_filters = target
                        .accounting_context
                        .query_params
                        .merge(
                          skip_equity_transfers: true,
                          from_date: period_beginning,
                          to_date: period_ending
                        )

      Money.new(account.report_balance(balance_filters))
    end
  end

  delegate :accounting_context, to: :target
  delegate :target, to: :scope
end
