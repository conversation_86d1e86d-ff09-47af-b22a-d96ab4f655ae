require 'rails_helper'

RSpec.describe 'tenant inspections', :js,
               capybara_login: :tenant do
  let!(:question) do
    create(:inspection_question,
           kind: :free_response,
           category: :unit,
           section: 'Entry Way',
           prompt: 'Intercom System')
  end

  let!(:template) { question.template }

  let!(:inspection) do
    create(:inspection_report,
           kind: :move_in,
           template: template,
           assigned_to: tenant)
  end

  let!(:record) do
    create(:inspection_record, report: inspection, target: tenant.current_unit)
  end

  scenario 'a user opens an inspection email' do
    perform_enqueued_jobs do
      TenantInspectionsMailer.move_in_inspection(inspection).deliver_later
    end

    open_email(tenant.email)
    current_email.click_link inspection.name
    expect(page).to have_content('Welcome')
  end

  scenario 'a user sees a move in inspection on their dashboard' do
    visit tenants_dashboard_path

    expect(page).to have_link(nil, href: tenants_inspection_path(inspection))
  end

  describe 'working on an inspection' do
    before do
      visit tenants_inspection_path(inspection)

      click_on 'Next'
    end

    scenario 'a user provides a response to an inspection question' do
      body = 'Broken'

      page.find('tr', text: question.prompt).click

      fill_in 'Condition', with: body

      click_on 'Save'

      expect(page).to have_content(body)

      response = Inspection::Response.last!
      expect(response.report).to eq(inspection)
      expect(response.question).to eq(question)
      expect(response.body).to eq(body)
    end

    scenario 'a user submits an inspection' do
      fill_in 'Comments', with: 'Overall good'

      click_on 'Next'

      fill_in_signature

      click_on 'Submit'

      expect(page).to have_content(/submitted successfully/i)
    end
  end

  context 'completed inspection' do
    before { inspection.complete! }

    let!(:response) do
      create(:inspection_response,
             question: question,
             record: record)
    end

    scenario 'a user sees a completed inspection in the documents tab' do
      visit tenants_documents_path

      expect(page).to have_link(nil, href: tenants_inspection_path(inspection))
    end

    scenario 'a user reviews a completed inspection' do
      visit tenants_inspection_path(inspection)

      expect(page).to have_content(response.body)
    end
  end
end
