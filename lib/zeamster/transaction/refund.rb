class Zeamster::Transaction::Refund < Zeamster::Service
  extend ::Service

  attr_reader :transaction

  def initialize(transaction:)
    @transaction = transaction
    @merchant_account = transaction.merchant_account
  end

  def call
    response = post(path, request)

    if response.success?
      ActiveRecord::Base.transaction do
        transaction.refunded!
        Payment::Reverse.call(payment)
      end

      success(transaction: transaction)
    else
      unsuccessful(response)
    end
  end

  private

  def request
    {
      transaction: {
        action: 'refund',
        payment_method: payment_method,
        previous_transaction_id: transaction.zeamster_id,
        transaction_amount: payment.transaction_amount.to_f,
        location_id: transaction.location_id
      }
    }
  end

  def path
    '/v2/transactions'
  end

  def payment_method
    if payment.ach?
      'ach'
    elsif payment.credit?
      'cc'
    else
      fail "Non-refundable payment type '#{payment.kind.humanize}'"
    end
  end

  delegate :payment, to: :transaction
end
