class ChartOfAccountsImporter::SubtypeTransformer
  SUBTYPES = {
    'Asset' => [
      'Bank',
      'Current Asset',
      'Fixed Asset',
      'Inventory',
      'Non-current Asset',
      'Prepayment'
    ],
    'Liability' => [
      'Current Liability',
      'Liability',
      'Non-current Liability'
    ],
    'Expense' => [
      'Depreciation',
      'Direct Costs',
      'Expense',
      'Overhead',
      'Recoverable Expense',
      'Non-recoverable Expense'
    ],
    'Equity' => [
      'Equity'
    ],
    'Revenue' => [
      'Other Income',
      'Revenue',
      'Sales',
      'Rent Income'
    ]
  }.freeze

  QUICKBOOKS_MAP = {
    'Accounts payable (A/P)' => 'Current Liability',
    'Accounts Payable' => 'Current Liability',
    'Accounts receivable (A/R)' => 'Current Asset',
    'Accounts Receivable' => 'Current Asset',
    'Cost of Goods Sold' => 'Overhead',
    'Credit Card' => 'Liability',
    'Expenses' => 'Expense',
    'Fixed Assets' => 'Fixed Asset',
    'Income' => 'Revenue',
    'Long Term Liabilities' => 'Non-current Liability',
    'Long Term Liability' => 'Non-current Liability',
    'Other Current Assets' => 'Current Asset',
    'Other Current Asset' => 'Current Asset',
    'Other Current Liabilities' => 'Current Liability',
    'Other Current Liability' => 'Current Liability',
    'Other Expense' => 'Expense'
  }.freeze

  def self.plutus_klass(value)
    cat = category(value)
    klass = SUBTYPES.find { |_, v| v.include?(cat) }&.first || value
    return nil if value.blank?

    "Plutus::#{klass}".safe_constantize
  end

  def self.category(value)
    QUICKBOOKS_MAP.fetch(value, value)
  end
end
