##
# Simply to allow services to receive #call directly on the class,
# which makes stubbing easier
module Service
  def call(*args, **kwargs, &)
    new(*args, **kwargs).call(&)
  end

  def call!(...)
    call(...).tap do |result|
      fail result.errors.join(', ') unless result.successful?
    end
  end

  def resp_success(**params)
    OpenStruct.new(successful?: true,
                   errors: [],
                   **params)
  end

  def resp_error(errors:)
    OpenStruct.new(successful?: false, errors: errors)
  end
end
