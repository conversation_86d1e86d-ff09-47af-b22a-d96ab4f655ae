= form_with id: :document_form,
  model: nil,
  url: url,
  class: 'ui form' do |f|

  .ui.info.message
    %p
      Clicking submit will generate and save a document from a template you
      select below.

    %p
      Optionally a copy of the document can also be delivered to the
      = human_contact_type
      via email.

  .field
    = f.label :template_id, 'Document Template'
    = f.semantic_dropdown :template_id,
      [['Select', nil]] + template_options,
      { selected: params[:template_id] },
      class: 'ui search selection dropdown',
      data: { options: { placeholder: 'Select' } }

  = render ElectronicDocuments::MetadataFieldsComponent.new f: f,
    document_templates: selected_template,
    remove_prefix: false

  .field{ class: class_names(disabled: email.blank?) }
    .ui.checkbox
      = f.check_box :electronic_delivery, value: nil
      = f.label :electronic_delivery,
        "Deliver via email to #{contact.name} (#{email || 'no email'})"

  - if additional_attachment_options.any?
    .field{ class: class_names(disabled: email.blank?) }
      = f.label :additional_attachment
      = f.semantic_dropdown :additional_attachment,
        [['None', nil]] + additional_attachment_options,
        {},
        class: 'ui search selection dropdown',
        data: { options: { placeholder: 'None' } }

  = f.submit 'Submit', class: 'ui primary submit button'

:javascript
  var form = $('form#document_form');

  form.find('.checkbox').checkbox();

  var xhr = null;

  form.find('select#template_id').dropdown('setting', 'onChange', function (id) {
    if (xhr) {
      xhr.abort();
    }

    form.find('.ui.submit.button').addClass('disabled');

    xhr = $.get(window.location.pathname + '.js?template_id=' + id);
  });
