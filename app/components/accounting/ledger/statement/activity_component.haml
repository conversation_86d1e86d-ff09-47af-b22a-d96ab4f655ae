.ui.very.basic.padded.vertical.segment
  .ui.very.basic.center.aligned.segment
    %span.ui.grey.text
      %i
        Activity from #{activity_start_date.to_fs(:short_date)} through #{activity_end_date.to_fs(:short_date)}

  %table.ui.very.basic.single.line.very.compact.small.table
    %thead
      %tr
        %th.center.aligned Date
        %th.center.aligned Description
        %th.center.aligned Reference
        %th.center.aligned Debits
        %th.center.aligned Credits
        %th.center.aligned Balance

    %tbody
      -# Beginning balance
      %tr
        %td{ colspan: 5 }
          %span.ui.grey.text
            %i Beginning Balance
        %td.right.aligned
          = beginning_balance.format

      - if entries.none?
        -# No activity
        %tr
          %td{ colspan: 6 }
            %span.ui.grey.text
              %i No activity in this period

      - entries.each do |entry|
        -# Entry rows
        %tr
          %td.center.aligned= entry.date.to_fs(:short_date)
          %td.center.aligned= truncate entry.description, length: 50
          %td.center.aligned= entry.reference
          %td.right.aligned= entry.debits&.format
          %td.right.aligned= entry.credits&.format
          %td.right.aligned= entry.balance.format

      -# Ending balance
      %tr
        %td{ colspan: 5 }
          %span.ui.grey.text
            %i Ending Balance
        %td.right.aligned
          %b= ending_balance.format
