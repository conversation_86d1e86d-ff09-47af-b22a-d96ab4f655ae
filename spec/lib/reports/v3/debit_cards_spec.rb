require 'rails_helper'

require_relative 'shared'

RSpec.describe Reports::V3::DebitCards do
  subject(:report) { described_class.new(user: user, filters: filters) }

  let(:user) { create(:property_manager, top_level:) }

  let(:top_level) { true }

  let(:filters) { OpenStruct.new }

  let(:company_one) { create(:company, name: 'Company One') }
  let(:company_two) { create(:company, name: 'Company Two') }

  let(:tunisia_deposit_account_one) do
    tunisia_deposit_account_one = create(:tunisia_deposit_account, tunisia_id: '1')
    tunisia_deposit_account_one.bank_account.update!(
      name: 'Tunisia Bank Account One',
      owner: company_one
    )

    tunisia_deposit_account_one
  end

  let(:tunisia_deposit_account_two) do
    tunisia_deposit_account_two = create(:tunisia_deposit_account, tunisia_id: '2')
    tunisia_deposit_account_two.bank_account.update!(
      name: 'Tunisia Bank Account Two',
      owner: company_two
    )

    tunisia_deposit_account_two
  end

  let!(:tunisia_debit_card_one) do # rubocop:disable RSpec/LetSetup
    create(:tunisia_debit_card, deposit_account: tunisia_deposit_account_one)
  end

  let!(:tunisia_debit_card_two) do
    create(:tunisia_debit_card, deposit_account: tunisia_deposit_account_two)
  end

  it_behaves_like 'a v3 report'

  describe 'when the user only has access to one company' do
    let(:top_level) { false }

    before do
      user.property_memberships.create!(target: company_one)
    end

    # Only shows debit card for company_one
    it_behaves_like 'a v3 report'
  end

  describe 'filters' do
    context 'when filtering by status' do
      let(:filters) { OpenStruct.new(status: 'Active') }

      before do
        tunisia_debit_card_two.update!(status: 'Inactive')
      end

      # Does not show tunisia_debit_card_two which is not active
      it_behaves_like 'a v3 report'
    end

    context 'when filtering by type' do
      let(:filters) { OpenStruct.new(type: 'physical') }

      before do
        tunisia_debit_card_two.update!(card_type: 'virtual')
      end

      # Only shows debit_card_one which is physical
      it_behaves_like 'a v3 report'
    end

    context 'when filtering by portfolio' do
      let(:filters) { OpenStruct.new(context_id: company_one.portfolio.to_sgid.to_s) }

      before do
        company_one.portfolio.update!(name: 'Company One Portfolio')
      end

      # Only shows debit_card_one which belongs to filtered portfolio
      it_behaves_like 'a v3 report'
    end

    context 'when filtering by entity' do
      let(:filters) { OpenStruct.new(context_id: company_one.to_sgid.to_s) }

      # Only shows debit_card_one which belongs to filtered entity
      it_behaves_like 'a v3 report'
    end
  end
end
