class Budget::Template
  extend Service

  attr_reader :journal, :year

  def initialize(journal:, year:)
    @journal = journal
    @year = year
  end

  def call
    workbook = RubyXL::Workbook.new
    sheet = workbook[0]

    sheet.sheet_name = 'Budget'
    write_header(sheet)
    write_accounts(sheet)

    workbook
  end

  private

  def write_header(sheet)
    sheet.add_cell(0, 0, 'Account')

    journal.fiscal_year(year).months.each_with_index do |month, index|
      sheet.add_cell(0, index + 1, month.strftime('%B'))
    end
  end

  def write_accounts(sheet)
    budget_accounts.each.with_index do |account, index|
      sheet.add_cell(index + 1, 0, account.display_name)
    end
  end

  def budget_accounts
    accounts.where(type: ['Plutus::Revenue', 'Plutus::Expense'])
  end

  delegate :accounts, to: :journal
end
