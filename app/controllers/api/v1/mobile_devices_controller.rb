##
# An endpoint for registering mobile devices as the corresponding firebase
# token for use with push notifications.
class Api::V1::MobileDevicesController < ApiController
  include Api::V1::AuthenticatedRoute

  def create
    device = MobileDevice.find_or_initialize_by(
      user: current_property_manager, firebase_token: params[:token]
    )

    if device.save
      render json: device, status: :created
    else
      render json: device.errors.full_messages, status: :unprocessable_entity
    end
  end

  def destroy
    device = MobileDevice.find_by!(
      user: current_property_manager, firebase_token: params[:token]
    )

    if device.destroy
      head :no_content
    else
      render json: device.errors.full_messages, status: :unprocessable_entity
    end
  end
end
