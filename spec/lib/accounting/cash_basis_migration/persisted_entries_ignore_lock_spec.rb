require 'rails_helper'

RSpec.describe 'cash basis migration persisted entries ignore lock' do
  scenario 'persisted cash basis entries do not lock' do
    journal = create(:company)

    entry = create(:journal_entry, journal: journal, date: 3.weeks.ago, basis: :accrual_basis_only)

    journal.update!(locked_at: Time.zone.today)

    expect(entry).to be_locked
    expect { entry.destroy! }.to raise_error(ActiveRecord::RecordNotDestroyed)

    entry = entry.reload
    entry.update!(basis: :all_bases)

    expect(entry).to be_locked
    expect { entry.destroy! }.to raise_error(ActiveRecord::RecordNotDestroyed)

    entry = entry.reload
    entry.update!(basis: :persisted_cash_basis_only)
    expect(entry).not_to be_locked
    entry.destroy!
  end
end
