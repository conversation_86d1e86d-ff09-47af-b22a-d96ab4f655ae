class CreateMessagingMessageDeliveries < ActiveRecord::Migration[6.1]
  def change
    create_table :messaging_message_deliveries do |t|
      t.references :message, null: false, foreign_key: { to_table: :messaging_emails }
      t.references :recipient, null: false, polymorphic: true
      t.integer :copied

      t.timestamps
    end

    reversible do |direction|
      direction.up do
        Messaging::Email.find_each do |message|
          Messaging::Message::Delivery.create!(
            message: message,
            recipient_type: message.recipient_type,
            recipient_id: message.recipient_id
          )
        end
      end

      direction.down do
        Messaging::Message::Delivery.find_each do |delivery|
          message = Messaging::Email.find(delivery.message_id)

          message.update!(
            recipient_id: delivery.recipient_id,
            recipient_type: delivery.recipient_type
          )
        end
      end
    end

    remove_reference :messaging_emails, :recipient, polymorphic: true
  end
end
