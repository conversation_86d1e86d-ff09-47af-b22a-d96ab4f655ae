class CustomForms::Automations::Payment::V1 <
  CustomForms::Automations
  class << self
    def create_automation_setting(form, params)
      merchant_account = MerchantAccount.with_credit_card.first || MerchantAccount.with_ach_debit.first
      receivable_account = Plutus::Account.where(type: 'Plutus::Revenue').first
      CustomForms::AutomationSettings::Payment::V1.create(
        form: form,
        charge_amount: params[:charge_amount],
        bank_account: merchant_account.bank_account,
        receivable_account: receivable_account
      )
    end

    def process_automation(submission, ip_address)
      payment_setting = CustomForms::AutomationSettings::Payment::V1.find_by(form: submission.form)
      CustomForms::ProcessPayment.call(submission, ip_address, payment_setting)
    end

    def position
      :bottom
    end

    def editor_component(setting)
      CustomForms::Automations::Payment::V1::EditorComponent.new(setting: setting)
    end

    def permitted_keys
      %i[charge_amount]
    end
  end
end
