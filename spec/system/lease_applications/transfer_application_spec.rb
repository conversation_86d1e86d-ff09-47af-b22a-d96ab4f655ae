require 'rails_helper'

RSpec.describe 'lease application transfers', :js,
               capybara_login: :property_manager do
  let!(:application) { create(:lease_application, :submitted) }

  context 'when applying to units' do
    let!(:unit_one) { application.unit }

    let!(:unit_two) { create(:unit, property: unit_one.property) }

    let!(:unit_occupied) { create(:unit, property: unit_one.property) }

    let!(:lease_occupied) do
      create(:lease, unit: unit_occupied, kind: :rollover)
    end

    scenario 'a user transfers a submitted lease application to a new unit' do
      visit leasing_application_path(application)

      click_actions
      click_action_item 'Transfer'

      select_dropdown 'Unit', unit_two.name

      click_on 'Submit'

      expect(page).to have_content(/updated successfully/i)

      expect(application.reload.unit).to eq(unit_two)
      expect(application.reload.floorplan).to eq(unit_two.floorplan)
      expect(application.reload.property).to eq(unit_two.property)
    end

    scenario 'cannot transfer to an occupied unit' do
      visit leasing_application_path(application)

      click_actions
      click_action_item 'Transfer'

      expect do
        select_dropdown 'Unit', unit_occupied.name
      end.to raise_error(Capybara::ElementNotFound)
    end
  end

  context 'when applying to floorplans' do
    let!(:other_floorplan_local) do
      create(:floorplan, property: application.property)
    end

    let!(:other_floorplan_remote) { create(:floorplan) }

    before do
      Configuration.update(application_target_kind: :apply_to_floorplans)

      visit leasing_application_path(application)

      click_actions
      click_action_item 'Transfer'
    end

    scenario 'transferring to another floorplan' do
      select_dropdown 'Floor Plan', other_floorplan_local.name

      click_on 'Submit'

      expect(page).to have_content(/successful/i)

      application.reload
      expect(application.property).to eq(other_floorplan_local.property)
      expect(application.floorplan).to eq(other_floorplan_local)
      expect(application.unit).to be_nil
    end

    scenario 'transferring to another property' do
      select_dropdown 'Property', other_floorplan_remote.property.name

      select_dropdown 'Floor Plan', other_floorplan_remote.name

      click_on 'Submit'

      expect(page).to have_content(/successful/i)

      application.reload
      expect(application.property).to eq(other_floorplan_remote.property)
      expect(application.floorplan).to eq(other_floorplan_remote)
      expect(application.unit).to be_nil
    end
  end
end
