class Customer::Billing::IncomeVerificationFee < Customer::Billing::Factor
  def line_items
    line_item(
      'Income Verification',
      Money.new(12_00),
      income_verifications.size
    )
  end

  def income_verifications
    query = TheClosingDocs::ScreeningGroup.where(created_at: billing_period)

    if @pricing_plan.single_invoice?
      query
    else
      query
        .joins(:lease_application)
        .where(lease_applications: { property: @company.properties })
    end
  end
end
