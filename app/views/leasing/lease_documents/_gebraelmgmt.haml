.ui.basic.padded.vertical.segment
  = f.fields_for :additional_fields, electronic_signable.metadata_form_model do |af|

    %h4 Designation of Utilities
    - %i[gas electric water trash].each do |utility|
      %h5= utility.to_s.titleize
      .three.fields
        .field
          = af.label "tenant_#{utility}", 'Tenant'
          = af.text_field "tenant_#{utility}"

        .field
          = af.label "landlord_#{utility}", 'Landlord'
          = af.text_field "landlord_#{utility}"

        .field
          = af.label "max_monthly_#{utility}", 'Max. Monthly'
          = af.money_field "max_monthly_#{utility}"

    .field
      = af.label :water_transfer_turn_on_date, 'Water / Sewer Transfer Turn On Date'
      = af.text_field :water_transfer_turn_on_date

    .field
      = af.label :consumers_energy_confirmation_number, 'Consumers Energy Confirmation Number'
      = af.text_field :consumers_energy_confirmation_number

    %h4 Appliance Responsibilities
    - %i[refrigerator stove dishwasher washer central_air].each do |appliance|
      %h5= appliance.to_s.titleize
      .three.fields
        .field
          = af.label "tenant_#{appliance}", 'Tenant'
          = af.text_field "tenant_#{appliance}"

        .field
          = af.label "landlord_#{appliance}", 'Landlord'
          = af.text_field "landlord_#{appliance}"

        .field
          = af.label "landlord_repair_#{appliance}", 'Landlord Repair'
          = af.text_field "landlord_repair_#{appliance}"

    %h4 Lead Based Paint Disclosure
    %h5 (A) Presence of lead-based paint and/or lead-based paint hazards
    .field
      .ui.checkbox
        = af.check_box :lead_known, {}, 'X'
        = af.label :lead_known, 'Known lead-based paint and/or lead-based paint hazards are present in the housing'

    .field
      = af.label :lead_known_explain, 'Explain'
      = af.text_area :lead_known_explain, rows: 3

    .field
      .ui.checkbox
        = af.check_box :lead_not_known, {}, 'X'
        = af.label :lead_not_known, 'Landlord has no knowledge of lead-based paint and/or lead-based paint hazards in the housing.'

    %h5 (B) Records and reports available to the landlord
    .field
      .ui.checkbox
        = af.check_box :lead_has_provided, {}, 'X'
        = af.label :lead_has_provided, 'Landlord has provided the tenant with all available records and reports pertaining to lead-based paint and/or lead-based paint hazards in the housing'

    .field
      = af.label :lead_has_provided_documents, 'List Documents'
      = af.text_area :lead_has_provided_documents, rows: 3

    .field
      .ui.checkbox
        = af.check_box :lead_no_reports, {}, 'X'
        = af.label :lead_no_reports, 'Landlord has no reports or records pertaining to lead-based paint and/or lead-based paint hazards in the housing.'

    %h4 Additional Information
    .three.fields
      .field
        = af.label :security_deposit_type
        = af.semantic_dropdown :security_deposit_type,
          ['Management Held', 'Owner Held']

      .field
        = af.label :security_deposit
        = af.money_field :security_deposit

      .field
        = af.label :number_of_pets, 'Number of Pets'
        = af.text_field :number_of_pets
