= form_with id: :payable_payment_form,
  model:,
  url:,
  local: false,
  remote: true,
  class: 'ui form',
  data: { controller: 'accounting--payables--payment-form' } do |f|

  = hidden_field_tag :updated_form, true

  = f.hidden_field :amount

  .two.required.fields
    .field
      = f.label :payer
      = f.semantic_search_field :payer,
        url: '/accounting/targets.json?q={query}',
        placeholder: 'Type to Search',
        data: { options: { type: 'category',
        cache: false, selectFirstResult: true },
        'accounting--payables--payment-form-target': 'payerSearch' }

    .field
      = f.label :payee
      = f.semantic_search_field :payee,
        url: '/accounting/targets.json?q={query}',
        placeholder: 'Type to Search',
        data: { options: { type: 'category',
        cache: false, selectFirstResult: true },
        'accounting--payables--payment-form-target': 'payeeSearch' }

  = render Accounting::Payables::MakePayment::DirectDepositUnavailableComponent.new(payment: f.object)

  .two.fields
    = render Accounting::Payables::MakePayment::WithdrawalAccountComponent.new(f:)

    = render Accounting::Payables::MakePayment::PaymentMethodComponent.new(f:)

  .fields
    = render Accounting::Payables::MakePayment::DepositAccountComponent.new(f:)

    .field{ class: class_names(f.object.mail_check? ? 'sixteen wide' : 'eight wide') }
      = f.label :description, 'Memo'
      = f.text_field :description, placeholder: memo_placeholder, data: { 'accounting--payables--payment-form-target': 'memo' }

    = render Accounting::Payables::MakePayment::CheckDetailsComponent.new(f:)

  = render Accounting::Payables::MakePayment::AddressVerificationComponent.new(payment: f.object)

  = render Accounting::Payables::MakePayment::InvoicesComponent.new(payment: f.object)

  = render Accounting::Payables::MakePayment::DisclaimerComponent.new(payment: f.object)

  = render Accounting::Payables::MakePayment::PrintReminderComponent.new(payment: f.object)

  = render Accounting::Payables::MakePayment::OverdraftWarningComponent.new(f:)

  .ui.error.message

  %button.ui.primary.submit.button{ disabled: submit_disabled?, data: { 'accounting--payables--payment-form-target': 'submitButton' } }
    Pay #{f.object.amount.format}

= render Accounting::Payables::MakePayment::EditAddressModalComponent.new(payment: model)

= render Accounting::Payables::MakePayment::VerifyAddressModalComponent.new(verification_info: flash[:verification_info])

:javascript
  $('.ui.checkbox').checkbox();
