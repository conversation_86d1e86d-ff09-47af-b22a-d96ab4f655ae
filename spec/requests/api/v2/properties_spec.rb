require 'swagger_helper'

require_relative 'rswag_context'

RSpec.describe 'api/v2/properties' do
  include_context 'rswag api v2'

  let!(:property) do
    create(:property).tap do |property|
      api_user.property_memberships.create!(target: property)
    end
  end

  path '/api/v2/properties' do
    get('list properties') do
      response(200, 'successful') do
        run_test!
      end
    end
  end

  path '/api/v2/properties/{id}' do
    parameter name: :id, in: :path, type: :integer, description: 'id'

    get('show property') do
      response(200, 'successful') do
        let(:id) { property.id }

        run_test!
      end
    end
  end
end
