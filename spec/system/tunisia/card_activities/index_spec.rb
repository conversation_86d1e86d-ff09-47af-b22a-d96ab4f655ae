require 'rails_helper'

RSpec.describe 'tunisia card activities index', :js, capybara_login: :property_manager do
  let(:company) { create(:company, :tunisia_customer) }
  let(:bank_account) { create(:bank_account, :tunisia, owner: company) }
  let(:chart_of_accounts) { company.chart_of_accounts }
  let!(:account) { create(:expense_account, name: 'Sample Account', tenant: chart_of_accounts) }
  let!(:property_one)  { create(:property, company: company, name: 'Property One') }
  let!(:property_two)  { create(:property, company: company, name: 'Property Two') }

  context 'when there are no debit card purchases' do
    before { visit organization_tunisia_bank_account_card_activities_path(bank_account) }

    it 'shows empty state' do
      expect(page).to have_content(/Your filters returned no results/i)
    end
  end

  context 'when there are debit card purchases' do
    let!(:debit_card_purchase_one) do
      create(:accounting_debit_card_purchase,
             :with_associations,
             bank_account: bank_account,
             transaction_at: 1.day.ago,
             property: property_one)
    end
    let!(:debit_card_purchase_two) do
      create(:accounting_debit_card_purchase,
             :with_associations,
             bank_account: bank_account,
             transaction_at: 2.days.ago,
             property: property_two)
    end

    before { visit organization_tunisia_bank_account_card_activities_path(bank_account) }

    context 'when filtering' do
      it 'filters by search' do
        expect(page).to have_content(debit_card_purchase_one.paid_by)
        expect(page).to have_content(debit_card_purchase_two.paid_by)

        fill_in 'Search...', with: debit_card_purchase_one.paid_by

        expect(page).to have_no_content(debit_card_purchase_two.paid_by)
        expect(page).to have_content(debit_card_purchase_one.paid_by)
      end

      it 'filters by paid_by' do
        filter_index 'Paid By', with: debit_card_purchase_one.paid_by

        expect(page).to have_no_content(debit_card_purchase_two.paid_by)
        expect(page).to have_content(debit_card_purchase_one.paid_by)
      end

      it 'filters by status' do
        debit_card_purchase_two.reviewed!
        visit organization_tunisia_bank_account_card_activities_path(bank_account)

        filter_index 'Status', with: 'Needs Review'

        expect(page).to have_no_content('Ready to Post')
        expect(page).to have_content('Needs Review')
      end

      it 'finds ignored transactions when filter is selected' do
        debit_card_purchase_two.archive!
        visit organization_tunisia_bank_account_card_activities_path(bank_account)

        expect(page).to have_content(debit_card_purchase_one.paid_by)
        expect(page).to have_no_content(debit_card_purchase_two.paid_by)

        filter_index 'Status', with: 'Ignored'

        expect(page).to have_content(debit_card_purchase_two.paid_by)
        expect(page).to have_no_content(debit_card_purchase_one.paid_by)
      end

      it 'filters by from date' do
        date = debit_card_purchase_one.transaction_at.to_date

        fill_in 'From', with: date

        within '.ui.popup.calendar.active' do
          find('td:not(.disabled)', text: /\A#{date.day}\Z/).click
        end

        expect(page).to have_no_content(debit_card_purchase_two.paid_by)
        expect(page).to have_content(debit_card_purchase_one.paid_by)
      end
    end

    context 'when performing bulk updates' do
      scenario 'User successfully performs bulk update with modal' do
        select_all_rows

        click_on_batch_action_button('Review Multiple')

        within '.visible.modal' do
          expect(page).to have_content('Review Transactions (2)')

          select_dropdown 'Account', account.name
          select_dropdown 'Vendor', debit_card_purchase_one.vendor.name
          select_dropdown 'Property', debit_card_purchase_one.property.name

          click_on 'Mark as Ready'
        end

        expect(page).to have_content('2 Transactions marked as Ready to Post')
        expect(debit_card_purchase_one.reload.account).to eq(account)
        expect(debit_card_purchase_two.reload.account).to eq(account)
        expect(debit_card_purchase_one.reload).to be_reviewed
        expect(debit_card_purchase_two.reload).to be_reviewed
      end

      context 'when company has multiple properties' do
        scenario 'User successfully performs bulk update with modal and sees property dropdown' do
          select_all_rows

          click_on_batch_action_button('Review Multiple')

          within '.visible.modal' do
            expect(page).to have_content('Review Transactions (2)')

            select_dropdown 'Account', account.name
            select_dropdown 'Vendor', debit_card_purchase_one.vendor.name
            select_dropdown 'Property', debit_card_purchase_one.property.name

            click_on 'Mark as Ready'
          end

          expect(page).to have_content('2 Transactions marked as Ready to Post')
          expect(debit_card_purchase_one.reload.property).to eq(property_one)
          expect(debit_card_purchase_two.reload.property).to eq(property_one)
        end
      end

      context 'when company has one property' do
        before do
          property_two.archive!
          visit organization_tunisia_bank_account_card_activities_path(bank_account)
        end

        scenario 'modal uses hidden field and auto-assigns property' do
          select_all_rows

          click_on_batch_action_button('Review Multiple')

          within '.visible.modal' do
            select_dropdown 'Account', account.name
            select_dropdown 'Vendor', debit_card_purchase_one.vendor.name

            click_on 'Mark as Ready'
          end

          expect(page).to have_content('2 Transactions marked as Ready to Post')
          expect(debit_card_purchase_one.reload.property).to eq(property_one)
          expect(debit_card_purchase_two.reload.property).to eq(property_one)
        end
      end

      context 'when some transactions are stale' do
        before do
          debit_card_purchase_one.update_column(:lock_version, 2) # rubocop:disable Rails/SkipsModelValidations
        end

        scenario 'user attempts to update' do
          select_all_rows

          click_on_batch_action_button('Review Multiple')

          within '.visible.modal' do
            expect(page).to have_content('Review Transactions (2)')

            select_dropdown 'Account', account.name
            select_dropdown 'Vendor', debit_card_purchase_one.vendor.name
            select_dropdown 'Property', debit_card_purchase_one.property.name

            click_on 'Mark as Ready'
          end

          expect(page)
            .to have_content('Another user has made changes to the selected transaction(s). ' \
                             'Please refresh and try again.')
        end
      end
    end

    context 'when archiving' do
      it 'archives a single debit card purchase' do
        expect(page).to have_content(debit_card_purchase_one.paid_by)
        expect(page).to have_content(debit_card_purchase_two.paid_by)

        select_row debit_card_purchase_one.paid_by

        click_on_batch_action_button('Ignore')

        within_modal do
          expect(page).to have_content 'Ignore Transactions'
          expect(page).to have_content(/ignore the 1 selected transaction?/i)
          click_button 'Yes, Ignore'
        end

        expect(page).to have_content(/1 Debit Card Purchase Ignored Successfully/i)
        expect(debit_card_purchase_one.reload).to be_archived
        expect(debit_card_purchase_two.reload).not_to be_archived
      end

      it 'archives multiple selected debit card purchases' do
        expect(page).to have_content(debit_card_purchase_one.paid_by)
        expect(page).to have_content(debit_card_purchase_two.paid_by)

        select_all_rows

        click_on_batch_action_button('Ignore')

        within_modal do
          expect(page).to have_content 'Ignore Transactions'
          expect(page).to have_content(/ignore the 2 selected transactions?/i)
          click_button 'Yes, Ignore'
        end

        expect(page).to have_content(/2 Debit Card Purchases Ignored Successfully/i)
        expect(debit_card_purchase_one.reload).to be_archived
        expect(debit_card_purchase_two.reload).to be_archived
      end

      it 'shows archived items with the correct status' do
        debit_card_purchase_two.archive!
        debit_card_purchase_two.reload
        visit organization_tunisia_bank_account_card_activities_path(bank_account)

        filter_index 'Status', with: 'Ignored'

        expect(find('#debit-card-purchases-index .ui.table')).to have_content('Ignored')
      end

      it "doesn't show actions for archived items" do
        debit_card_purchase_two.archive!
        debit_card_purchase_two.reload
        visit organization_tunisia_bank_account_card_activities_path(bank_account)

        expect(find('.ui.table')).to have_content('Review')

        filter_index 'Status', with: 'Ignored'

        expect(find('#debit-card-purchases-index .ui.table')).to have_no_content('Review')
        expect(find('#debit-card-purchases-index .ui.table')).to have_no_content('Edit')
      end

      it 'unarchives multiple selected debit card purchases' do
        debit_card_purchase_one.archive!
        debit_card_purchase_two.archive!
        debit_card_purchase_one.reload
        debit_card_purchase_two.reload
        visit organization_tunisia_bank_account_card_activities_path(bank_account)

        filter_index 'Status', with: 'Ignored'
        select_row debit_card_purchase_one.paid_by

        click_on_batch_action_button('Unignore')

        expect(page).to have_content(/1 Debit Card Purchase Unignored Successfully/i)
        expect(debit_card_purchase_one.reload).not_to be_archived
        expect(debit_card_purchase_two.reload).to be_archived
      end
    end
  end
end
