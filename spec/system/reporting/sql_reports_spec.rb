require 'rails_helper'

RSpec.describe 'sql reports',
               capybara_login: :property_manager do
  let!(:property) { create(:property) }

  let!(:report) do
    Report::SQL.create!(
      name: 'Property Names',
      description: 'The name of each property',
      sql_query: 'select name from properties;'
    )
  end

  scenario 'a user sees the report in the reports list' do
    visit reports_path
    expect(page).to have_content(report.name)
  end

  describe 'looking at the report' do
    before { visit report_path(report) }

    scenario 'a user looks at the report title' do
      expect(page).to have_content(report.name)
    end

    scenario 'a user looks at evaluated sql' do
      expect(page).to have_content(property.name)
    end
  end
end
