.cf-element.cf-field{data: field.editor_json }
  = render CustomForms::Element::BaseComponent.new(item: field) do |c|
    - c.with_editor_label do
      = 'Date'
    - c.with_icon do
      %i.calendar.icon.outline
    - c.with_canvas_preview do
      .attached.content
        .ui.form
          .field{class: class_names('required' => element.required)}
            %label
              = element.label
            .ui.calendar
              .ui.input.left.icon
                %i.calendar.icon
                %input{type:'text', placeholder: element.placeholder }
    - c.with_details_form do
      = render partial: 'custom_forms/editor/shared/text_field_details_form', locals: { element: element }
