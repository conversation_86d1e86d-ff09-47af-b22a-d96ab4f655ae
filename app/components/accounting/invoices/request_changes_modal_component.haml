= render ModalComponent.new id: :request_invoice_changes_modal,
  title: 'Request Changes',
  options: { form: :request_invoice_changes_form } do

  .ui.top.embedded.info.message
    %p
      Clicking submit will send a change request notification including your
      message to the selected user.

  = form_with id: :request_invoice_changes_form,
    local: false,
    model: Approvals::ChangeRequest.new,
    url: approvals_change_requests_path(approvable_sgid: invoice.to_sgid.to_s),
    scope: :change_request,
    class: 'ui form' do |f|

    .required.field
      = f.label :requested_from_id, 'User'
      = f.semantic_dropdown :requested_from_id,
        user_options,
        {},
        class: 'ui search selection dropdown',
        data: { options: { placeholder: 'Select' } }

    .required.field
      = f.label :message
      = f.text_area :message,
        rows: 3,
        placeholder: 'Describe the problem with this invoice'

    .ui.bottom.embedded.error.message
