- require_permission :update_organization_roles? do
  = link_to 'Edit',
    edit_organization_role_path(@role),
    class: 'right floated ui button'

- require_permission :destroy_organization_roles? do
  = link_to 'Delete',
    organization_role_path(@role),
    class: 'right floated ui button',
    method: :delete,
    data: { confirm: 'Delete Role?' }

.ui.breadcrumb
  = link_to 'Roles', organization_roles_path, class: 'ui section'
  %i.right.angle.icon.divider

  %h1.ui.header
    = @role.name

%h3.ui.dividing.header
  Permissions

%h3.ui.dividing.header
  Users

.ui.selection.list
  = render partial: 'search/property_manager',
    collection: @role.users.order(last_name: :asc)
