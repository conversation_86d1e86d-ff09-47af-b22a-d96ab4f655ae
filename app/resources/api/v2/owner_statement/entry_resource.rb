class Api::V2::OwnerStatement::EntryResource < JSONAPI::Resource
  include Api::V2::Shared

  model_name 'Api::V2::OwnerStatementEntry'

  attributes :date, :description, :contact, :reference
  attributes :cash_in, :cash_out, :balance

  has_one :property, exclude_links: :default

  exclude_links :default

  def self.records(options = {})
    journal = options[:context][:journal]

    super(options).where(entity_id: journal.id)
  end
end
