class Accounting::Context::Vendor < Accounting::Context::Base
  attr_reader :vendor, :property, :company

  def initialize(vendor:, company: nil, property: nil)
    @vendor = vendor
    @property = property
    @company = company || property.company
  end

  def entry_params
    {
      journal_id: company.id,
      property_id: property&.id
    }.compact
  end

  def as_json(options = {})
    {
      id: vendor.to_sgid.to_s,
      name: vendor.name,
      type: vendor.class.name,
      chart_of_accounts_id: chart_of_accounts.id
    }
  end

  delegate :chart_of_accounts, to: :company

  alias entity vendor
  alias journal company
end
