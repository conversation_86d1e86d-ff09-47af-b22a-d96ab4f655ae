class MakeAssignmentsPolymorphicAssignable < ActiveRecord::Migration[5.0]
  def change
    change_table :assignments do |t|
      # Make polymorphic
      t.rename :task_id, :assignable_id
      t.string :assignable_type

      # Add non nulls
      reversible do |dir|
        dir.up do
          t.change :assignable_id, :integer, null: false
          t.change :user_id, :integer, null: false

          Assignment.update_all(assignable_type: 'Task')

          t.change :assignable_type, :string, null: false
        end

        dir.down do
          t.change :task_id, :integer, null: true
          t.change :user_id, :integer, null: true
        end
      end

      # Update indices
      t.remove_index [:assignable_id]
      t.index [:assignable_id, :assignable_type]
    end

    # Remove foreign key
    remove_foreign_key :assignments, :tasks
  end
end
