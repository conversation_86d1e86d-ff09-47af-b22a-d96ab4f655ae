require 'rails_helper'

RSpec.describe CustomForms::Element::TextArea do
  describe 'the factory' do
    subject { build(:custom_forms_element_text_area) }

    it { is_expected.to be_valid }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:label) }
    it { is_expected.to validate_presence_of(:placeholder) }
  end

  describe 'validate_submitted_response' do
    let(:label) { 'text area label' }

    let(:text_area_element) do
      create(:custom_forms_element_text_area, required: required, label: label)
    end

    let(:result) { text_area_element.validate_submitted_response(value) }

    context 'when it is required' do
      let(:required) { true }

      context 'when an empty string is received' do
        let(:value) { '' }

        it 'returns a message' do
          expect(result).to eq 'Text area label is required'
        end
      end

      context 'when a nonempty string is received' do
        let(:value) { 'a1j4' }

        it 'returns nil' do
          expect(result).to be_nil
        end
      end
    end

    context 'when it is not required' do
      let(:required) { false }

      context 'when an empty string is received' do
        let(:value) { '' }

        it 'returns nil' do
          expect(result).to be_nil
        end
      end

      context 'when a nonempty string is received' do
        let(:value) { 'a1j4' }

        it 'returns nil' do
          expect(result).to be_nil
        end
      end
    end
  end
end
