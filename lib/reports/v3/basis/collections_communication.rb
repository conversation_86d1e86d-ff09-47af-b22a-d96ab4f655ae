class Reports::V3::Basis::CollectionsCommunication < Reports::V3::Basis::Base
  def relation
    Collections::Communication.all
  end

  def joined(query)
    query = query.includes(:tenant) if mapping.key?(:contact)

    query
  end

  def ordered(query)
    query.reorder(created_at: :desc)
  end

  def filtered(query)
    query = query.where(status: filters.status) if filters.status.present?

    if filters.start_date.present?
      query = query.where(created_at: filters.start_date..)
    end

    if filters.end_date.present?
      query = query.where(created_at: ..filters.end_date.end_of_day)
    end

    query
  end

  column :date do
    { value: created_at.to_date, justify: :center }
  end

  column :channel do
    value = channel == 'sms' ? 'SMS' : channel.titleize

    { value: value }
  end

  column :status do
    { value: status.titleize }
  end

  column :processed_at do
    { value: processed_at, justify: :center }
  end

  column :contact do
    { value: tenant.name, link: tenant.url }
  end

  column :balance do
    { value: balance }
  end
end
