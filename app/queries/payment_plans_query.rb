class PaymentPlansQuery
  def initialize(relation = PaymentPlan.all)
    @relation = relation.extending(Scopes)
  end

  def search
    @relation
  end

  module Scopes
    def by_user(user)
      return self if user.top_level?

      by_properties(user.properties)
    end

    def by_properties(properties)
      where(simple_agreement: { property: properties })
        .or(where(lease_membership: { leases: { units: { property: properties } } }))
    end

    def by_tenant(tenant)
      query = left_joins(:lease_membership)

      query.where(simple_agreement: tenant.simple_agreements).or(
        query.where(lease_memberships: { leases: tenant.leases })
      )
    end

    def by_invoice(invoice)
      memberships = PaymentPlan::InvoiceMembership.where(
        invoice: invoice
      )

      where(id: memberships.select(:payment_plan_id))
    end

    def active(date = Time.zone.today)
      where(start_date: ..date, end_date: date..)
    end

    def with_current_or_upcoming_installments(date = Time.zone.today)
      installments = PaymentPlan::Installment.where(date: date..)

      where(id: installments.select(:payment_plan_id))
    end
  end
end
