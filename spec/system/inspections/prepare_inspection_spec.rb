require 'rails_helper'

RSpec.describe 'new inspections', :elasticsearch, :js,
               capybara_login: :property_manager do
  let!(:template) { create(:inspection_template) }

  let!(:property) do
    perform_enqueued_jobs { create(:property, kind: :singlefamily) }
  end

  let!(:unit) { create(:unit, property: property) }

  before do
    perform_enqueued_jobs do
      Searchable::Index.all
    end
  end

  scenario 'a user prepares a rehab inspection' do
    visit operations_inspections_path

    click_on 'New Inspection'

    select_dropdown 'Inspection Type', 'Rehab'
    select_dropdown 'Template', template.name

    # Search for property
    fill_in 'Inspection Area', with: property.name
    find('.title', text: property.name).click

    fill_in_calendar '#inspection_report_due_date', with: 1.day.from_now

    click_on 'Submit'

    name = "Inspect #{property.name}"

    expect(page).to have_content(name)

    inspection = Inspection::Report.last!
    expect(inspection.name).to eq(name)
    expect(inspection.template).to eq(template)
    latest_activity = inspection.activities.last
    expect(latest_activity.kind).to eq('inspection_opened')
    record = inspection.records.first!
    expect(record.target).to eq(unit)
  end

  scenario 'a user prepares a unit inspection' do
    visit operations_inspections_path

    click_on 'New Inspection'

    select_dropdown 'Inspection Type', 'Rehab'
    select_dropdown 'Template', template.name

    # Search for unit
    fill_in 'Inspection Area', with: unit.name
    find('.title', text: unit.name).click

    select_dropdown 'Assignee', 'Employee'
    search_for manager.name, in: 'Assignee'

    fill_in_calendar '#inspection_report_due_date', with: 1.day.from_now

    click_on 'Submit'

    name = "Inspect #{unit.name}"

    expect(page).to have_content(name)

    inspection = Inspection::Report.last!
    expect(inspection.name).to eq(name)
    expect(inspection.template).to eq(template)
    expect(inspection.assigned_to).to eq(manager)

    # Activity
    latest_activity = inspection.activities.last
    expect(latest_activity.kind).to eq('inspection_opened')

    # Record
    record = inspection.records.first!
    expect(record.target).to eq(unit)

    # Notification
    notification = manager.notifications.last!
    expect(notification).to have_attributes(
      kind: 'inspection_assignment',
      resource: inspection,
      title: 'You were assigned to an inspection',
      description: inspection.name
    )
  end
end
