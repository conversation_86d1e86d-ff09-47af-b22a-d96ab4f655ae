class CreateApprovalsApprovals < ActiveRecord::Migration[5.2]
  def change
    create_table :approvals_approvals do |t|
      t.references :approvable, polymorphic: true, null: false, index: { name: :index_approvals_on_approvable }
      t.references :approved_by, polymorphic: true, null: false, index: { name: :index_approvals_on_approved_by }
      t.references :rule, foreign_key: { to_table: :approvals_rules }, null: false

      t.timestamps
    end
  end
end
