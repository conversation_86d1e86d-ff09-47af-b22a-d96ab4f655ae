module ActionsMenu::UserMasquerading
  protected

  def masquerade
    return unless Feature.enabled?(:user_masquerading, Customer.current)

    {
      text: 'Masquerade',
      icon: 'theater masks',
      link: masquerade_path(sgid: resource.to_sgid.to_s),
      disabled: !masqueradable?,
      options: {
        target: '_blank',
        method: :post
      },
      permission: masquerade_as_permission?
    }
  end

  def masqueradable?
    true
  end

  def masquerade_as_permission?
    true
  end
end
