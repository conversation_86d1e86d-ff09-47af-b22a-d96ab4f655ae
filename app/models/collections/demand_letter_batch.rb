class Collections::DemandLetterBatch < ApplicationRecord
  belongs_to :created_by, class_name: 'PropertyManager',
                          inverse_of: :collections_demand_letter_batches,
                          optional: false

  has_many :demand_letters, dependent: :nullify
  has_many :leases, through: :demand_letters

  # TODO: validate at least one

  def manual_deliveries
    demand_letters.where(deliver_electronically: false)
  end

  def electronic_deliveries
    demand_letters.where(deliver_electronically: true)
  end
end
