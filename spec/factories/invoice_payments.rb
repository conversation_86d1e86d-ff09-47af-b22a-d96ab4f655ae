FactoryBot.define do
  factory :invoice_payment do
    amount_cents { rand(100..100_000) }

    # NOTE: this block is needed because InvoicePayment validates that its
    # payment and invoice match up to a certain extent
    after(:build) do |ip|
      if ip.invoice.present? && ip.payment.blank?
        ip.payment = build(:payment,
                           invoice_payment_count: 0,
                           date: ip.invoice.post_date,
                           payer: ip.invoice.buyer,
                           payee: ip.invoice.seller,
                           payer_lease_membership:
                           ip.invoice.buyer_lease_membership,
                           payee_lease_membership:
                           ip.invoice.seller_lease_membership,
                           amount: ip.amount)
      elsif ip.payment.present? && ip.invoice.blank?
        ip.invoice = build(:invoice,
                           date: ip.payment.date,
                           buyer: ip.payment.payer,
                           seller: ip.payment.payee,
                           buyer_lease_membership:
                           ip.payment.payer_lease_membership,
                           seller_lease_membership:
                           ip.payment.payee_lease_membership,
                           amount: ip.amount)
      elsif ip.payment.blank? && ip.invoice.blank?
        invoice = build(:invoice, amount: ip.amount)
        payment = build(:payment, invoice_payment_count: 0,
                                  amount: ip.amount,
                                  payer: invoice.buyer,
                                  payee: invoice.seller,
                                  payer_lease_membership:
                                  invoice.buyer_lease_membership,
                                  payee_lease_membership:
                                  invoice.seller_lease_membership)

        ip.invoice = invoice
        ip.payment = payment
      end
    end
  end
end
