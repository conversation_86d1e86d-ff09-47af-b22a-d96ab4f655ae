:ruby
  @account_options = [['Select', nil]] + @journal.accounts.map { |a| [a.display_name, a.id] }
  path = if @entry.new_record?
           accounting_journal_entries_path(@journal)
         else
           accounting_journal_entry_path(@journal, @entry, basis: params[:basis])
         end

= form_with id: :entry_form,
  url: path,
  model: @entry,
  scope: :entry,
  class: 'ui form' do |f|

  .ui.two.column.divided.relaxed.stackable.grid
    .thirteen.wide.column
      .three.fields
        .required.field
          = f.label :description, 'Memo'
          = f.text_field :description

        .required.field
          = f.label :date
          = f.semantic_date_field :date

        .field
          = f.label :property_id
          = f.semantic_dropdown :property_id,
            [['None', '']] + @journal.properties.map { |p| [p.name, p.id] },
            {},
            class: 'ui fluid search selection dropdown',
            data: { options: { placeholder: 'None' } }

      .field
        = f.label :attachments
        = f.file_field :attachments, multiple: true, tabindex: -1

      - if @entry.persisted? && @entry.attachments.any?
        .ui.list
          - @entry.attachments.each do |attachment|
            .item
              = attachment.filename
              = link_to "(Remove)",
                "/attachments/#{@entry.to_sgid.to_s}/#{attachment.id}",
                method: :delete,
                data: { confirm: 'Remove attachment?' }

      %h4.ui.dividing.header
        Amounts

      #amounts.ui.vertical.segment
        = f.fields_for :amounts do |a|
          = render 'amount_fields', f: a

        .clearfix
          = link_to_add_association f,
            :amounts,
            class: 'right floated ui basic button' do
            %i.plus.icon
            Add Amount

      .ui.grid.container.basic.vertical.segment
        .five.wide.column
          %h4 Total
        .five.wide.column#debit-total
          $0.00
        .five.wide.column#credit-total
          $0.00

    .three.wide.column
      .field
        = f.label :basis
        = f.semantic_dropdown :basis,
          [['All', 'all_bases'],
          ['Accrual Only', 'accrual_basis_only'],
          ['Cash Only', 'cash_basis_only']],
          {},
          class: 'ui fluid selection dropdown',
          data: { options: { allowTab: false } }

      .field
        %label Special
        .ui.checkbox
          = f.check_box :retained_earnings, tabindex: -1
          = f.label :retained_earnings, 'This is a Closing Entry'

      .field
        .ui.checkbox
          = f.check_box :reverse_automatically,
            checked: f.object.automatic_reversal.present?,
            tabindex: -1
          = f.label :reverse_automatically

    .clearfix
      - if @entry.new_record?
        = f.submit 'Save', class: 'ui primary submit button'
        = f.submit 'Save and New', class: 'ui basic submit button', name: :new
        = f.submit 'Save and Copy', class: 'ui basic submit button', name: :copy
      - else
        = f.submit 'Update', class: 'ui primary submit button'

:javascript
  var computeTotals = function () {
    var debits = 0;
    var credits = 0;

    // Visible to prevent computing from removed entries
    $('#entry_form input[name$="debit_amount]"]:visible').each(function () {
      debits += Math.round(parseFloat(sanitizeMoney($(this).val() || 0, 10)) * 100);
    });

    $('#entry_form input[name$="credit_amount]"]:visible').each(function () {
      credits += Math.round(parseFloat(sanitizeMoney($(this).val() || 0, 10)) * 100);
    });

    $('#debit-total').html(formatMoney(debits, true));
    $('#credit-total').html(formatMoney(credits, true));

    // if (debits === credits) {
    //   $('#entry_form .submit.button').removeClass('disabled');
    // } else {
    //   $('#entry_form .submit.button').addClass('disabled');
    // }
  };

  var setupAmountFields = function () {
    $('#entry_form input[name$="amount]"]').on('keyup', computeTotals);
  };

  $('#amounts')
    .on('cocoon:before-insert', function (e, fields) {
      fields.hide();
    })
    .on('cocoon:after-insert', function (e, fields) {
      fields.slideDown(150);
      initializeSemanticFields();
      setupAmountFields();
    })
    .on('cocoon:before-remove', function (e, fields) {
      $(this).data('remove-timeout', 150);
      fields.animate({ height: 0, opacity: 0 }, 150);
    })
    .on('cocoon:after-remove', function () {
      computeTotals();
    });

  $('#entry_form').form({
    on: 'submit',
    inline: true,
    fields: {
      entry_date: ['empty'],
      entry_description: ['empty'],
      amount_account: ['empty'],
    },
  });

  $('.ui.checkbox').checkbox();

  setupAmountFields();
  computeTotals();
