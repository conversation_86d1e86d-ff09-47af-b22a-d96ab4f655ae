- brown = Customer.current_subdomain == 'brown'

.ui.container
  .ui.very.basicsegment
    %img.ui.right.floated.tiny.image{ src: brand.logo_url }

  %h1.ui.header
    - if brown
      - if params['form'] == 'graduate'
        Application for Graduate Housing
      - else
        Visiting Scholars’ Housing Reservation Form
    - else
      New Guest Information Card
    .sub.header
      = brand.name

  - if brown
    .ui.container
      - if params['form'] == 'graduate'
        %p
          Rental units will be offered on a first-come first-serve basis with
          priority given to first-year graduate students. A 12-month term is required.

        %p
          Applicants will be notified of their housing assignment (via the
          email address provided) within ten (10) days of submitting this
          application. 
          %b 
            If you don't receive a housing assignment offer within
            10 days, there is no availability. You'll be placed on a waitlist and
            notified should a cancellation arise.
          Those applicants who are not 1st-year graduates will be offered
          housing should any be available, after May 5, 2025.

        %p
          To accept the housing assignment, email your decision within five (5) days of the offer.
          Once your acceptance is received, your student Banner account will be
          charged the required $500 security deposit and a housing agreement will be emailed to you for your signature. 
          %b
            If the housing agreement is not returned by
            that date, the offer will be retracted and offered to the next applicant.

        %p
          All assignments are made without regard to race, color, national origin, religion, sex, age, family status, marital status, or disability.

        %p
          %b
            PROHIBITED: Subletting, smoking /vaping, and pets (except service
            animals or emotional support animals registered through SAS)
      - else
        %p
          Visiting Scholar Short-Term Housing arrangements must be reserved by
          the Brown University Department sponsoring the visiting guest on
          University-related business. Reservations must be made prior to
          completing this form. Reservations will be confirmed only upon receipt
          of the completed form.
        %p
          Rent must be paid directly by the visiting scholar using his/her
          personal funds. Federal tax regulations place restrictions on direct
          rent payment by a University department for more than one month.
          Should the sponsoring department wish to subsidize the cost of
          housing for the guest, suitable arrangements are to be negotiated
          directly with the guest.
        %p
          Single-occupancy accommodations can be reserved on a daily, weekly or
          monthly basis.
          %b
            %u
              We cannot accommodate children or pets. Smoking is prohibited in
              all Visiting Scholar units.
          Any guest violating this policy will be asked to leave the premises
          and will relinquish any rent that has been received.
        %p
          The rental agreement is between the Auxiliary Housing Office and the
          Brown University Department sponsoring the guest. The sponsoring
          department will serve as the official contact point on behalf of its
          guest and accepts full responsibility for any loss and/or damage to
          the property; for any unpaid rent and for any expense incurred for
          last-minute schedule changes.
        %p
          %ul
            %li
              Cancellations made one week or less prior to the date of arrival,
              the Sponsoring Department will be charged one week’s rent.
            %li
              Cancellations made after the guest has arrived, the guest will be
              charged one month’s rent.
            %li
              Guests arriving after the stated reserved arrival date below will
              be charged from the original arrival date.
      .ui.checkbox#agree-checkbox
        %input{ type: :checkbox }
        %label I agree to the above terms

      %br
      %br
      :javascript
        $(document).on('turbolinks:load', function() {
          var selector = '.guest-card.segment';

          $(selector).addClass('disabled');

          $('.ui.checkbox').checkbox();

          $('#agree-checkbox').checkbox({
            onChecked: function() {
              $(selector).removeClass('disabled');
            },
            onUnchecked: function() {
              $(selector).addClass('disabled');
            }
          });
        });
      :css
        .segment.disabled .ui.field,
        .segment.disabled .button {
          pointer-events: none;
        };

  :ruby
    options = {}
    if params[:form] == 'graduate'
      options = {
        selectProperty: false,
        selectFloorplan: false,
        selectSource: false,
        extraFields: [
          { label: 'Academic Department', name: 'department' },
          {
            label: 'Banner ID# (B...)',
            name: 'banner_id',
            validate: 'guest_card_banner_id',
            required: 'true'
          },
          {
            label: 'Present Address',
            fields: [
              { label: 'Street', name: 'present_address[street]' },
              { label: 'City', name: 'present_address[city]' },
              { label: 'State', name: 'present_address[state]' },
              { label: 'Country', name: 'present_address[country]' },
              { label: 'Postal Code', name: 'present_address[postal_code]' }
            ]
          },
          {
            fields: [],
            type: 'gender'
          },
          {
            label: 'Option 1: Studio Apartment',
            name: 'option_1][preferences',
            units: [
              {
                name: '71-73_charles_field_street_efficiency',
                label: '71-73 Charlesfield',
                description: 'Studio (225 sq. ft.)',
                price: '$1,250 / month',
                restriction: '(1 person)'
              },
              {
                name: '86_waterman_street_efficiency',
                label: '86 Waterman',
                description: 'Studio (225 sq. ft.)',
                price: '$1,250 / month',
                restriction: '(1 person)'
              },
              {
                name: '165_angell_street_efficiency',
                label: '165 Angell',
                description: 'Studio (325 sq. ft.)',
                price: '$1,250 / month',
                restriction: '(1 person)'
              }
            ]
          },
          {
            label: 'Option 2: One Bedroom Apartment',
            name: 'option_2][preferences',
            units: [
              {
                name: '172_cushing_one_bedroom',
                label: '172 Cushing',
                description: '1-Bedroom (375 sq. ft.)',
                price: '$1365 / month',
                restriction: '1 person'
              },
              {
                name: '127_angell_one_bedroom',
                label: '127 Angell',
                description: '1-Bedroom (427 sq. ft.)',
                price: '$1365 / month',
                restriction: '1 person'
              },
              {
                name: '20_olive_one_bedroom',
                label: '20 Olive',
                description: '1-Bedroom (601 sq. ft.)',
                price: '$1365 / month',
                restriction: '1 person'
              },  
              {
                name: '84_benevolent_one_bedroom',
                label: '84 Benevolent',
                description: '1-Bedroom (373 sq. ft.)',
                price: '$1365 / month',
                restriction: '1 person'
              },

              
            ]
          },
          {
            label: 'Option 3: Shared Multi-Bedroom Apartment/House',
            name: 'option_3][preferences',
            units: [
              {
                label: 'APARTMENTS'
              },
              {
                name: '20_olive_street_two_bedroom',
                label: '20 Olive',
                description: '2-Bedroom (800 sq. ft.)',
                price: '$1,150 / person / month',
                restriction: '2 persons'
              },
              {
                name: '20_olive_street_four_bedroom',
                label: '20 Olive',
                description: '4-Bedroom (1507 sq. ft.)',
                price: '$950 / person / month',
                restriction: '4 persons'
              },
              {
                name: '74_80_benevolent_two_bedroom',
                label: '74 - 80 Benevolent',
                description: '2-Bedroom (775 sq. ft.)',
                price: '$1,150 / person / month',
                restriction: '2 persons'
              },
              {
                name: '84_benevolent_two_bedroom',
                label: '84 Benevolent',
                description: '2-Bedroom (674 sq. ft.)',
                price: '$1,150 / person / month',
                restriction: '2 persons'
              },
              {
                name: '127_angell_three_bedroom',
                label: '127 Angell',
                description: '3-Bedroom (850 sq. ft.)',
                price: '$1,050 / person / month',
                restriction: '3 persons'
              },
              {
                name: '84_benevolent_three_bedroom',
                label: '84 Benevolent',
                description: '3-Bedroom (929 sq. ft.)',
                price: '$1,050 / person / month',
                restriction: '3 persons'
              },
              {
                name: '76_benevolent_three_bedroom',
                label: '76 Benevolent',
                description: '3-Bedroom (756 sq. ft.)',
                price: '$1050 / person / month',
                restriction: '3 persons'
              },
              {
                name: '86_benevolent_three_bedroom',
                label: '86 Benevolent',
                description: '3-Bedroom (1200 sq. ft.)',
                price: '$1050.00 / person / month',
                restriction: '3 persons'
              },
              {
                name: '281_283_brook_three_bedroom',
                label: '281-283 Brook',
                description: '3-Bedroom (1200 sq. ft.)',
                price: '$1050 / person / month',
                restriction: '3 persons'
              },
              {
                label: 'HOUSES'
              },
              {
                name: '129_angell_four_bedroom',
                label: '129 Angell',
                description: '4-Bedroom House (1950 sq. ft.)',
                price: '$1350 / person / month',
                restriction: '4 persons'
              },
              {
                name: '154_cushing_four_bedroom',
                label: '154 Cushing',
                description: '4-Bedroom House (2400 sq. ft.)',
                price: '$1350 / person / month',
                restriction: '4 persons'
              },
              {
                name: '166_cushing_street_four_bedroom',
                label: '166 Cushing Street',
                description: '4-Bedroom House (2600 sq. ft.)',
                price: '$1350 / person / month',
                restriction: '4 persons'
              },
              {
                name: '129_waterman_four_bedroom',
                label: '129 Waterman',
                description: '4-Bedroom House (1983 sq. ft.)',
                price: '$1350 / person / month',
                restriction: '4 persons'
              }
            ]
          }
        ],
        startDates: [
          'June 2, 2025', 'July 7, 2025', 'August 4, 2025'
        ],
        extraParams: [
          { name: 'form', value: 'graduate' },
          { name: 'source', value: 'Graduate Housing Form' }
        ]
      }
    elsif params[:form] == 'visiting_scholar'
      options = {
        selectProperty: false,
        selectFloorplan: false,
        selectSource: false,
        extraFields: [
          { label: '# of Occupants', name: 'occupants' },
          { label: 'From / Country', name: 'country' },
          { label: 'Department', name: 'department' },
          { label: 'Department Contact Person', name: 'department_contact' },
          { label: 'BATkey Number (if department is paying)', name: 'batkey_number' },
          { label: 'Phone', name: 'department_phone' },
          { label: 'Box', name: 'department_box' },
          { label: 'Fax', name: 'department_fax' }
        ],
        extraParams: [
          { name: 'form', value: 'visiting_scholar' },
          { name: 'source', value: 'Visiting Scholar Form' },
        ]
      }
    end

  .ui.container{ class: brown ? nil : 'text' }
    = react_component 'NewGuestCard',
      action: '/guests',
      recaptcha: Rails.env.production?,
      options: options
