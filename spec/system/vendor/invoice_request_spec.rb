require 'rails_helper'

require_relative '../shared/invoice_filling_context'

RSpec.describe 'vendor invoice requests', :js do
  let(:assignment) do
    create(:vendor_assignment).tap do |assignment|
      property = create(:property)
      tenant = create(:tenant)
      create(:expense_account, tenant: property.company.chart_of_accounts)
      assignment.maintenance_ticket.update!(property: property, tenant: tenant)
    end
  end

  let(:ticket) { assignment.maintenance_ticket }

  let(:property) { ticket.property }

  let(:vendor) { assignment.vendor }

  let(:employee) { assignment.created_by }

  # Define #maintenance_billing? as false
  before { Customer.current.update!(kind: :owner_operator) }

  scenario 'a vendor is sent an email upon ticket completion',
           capybara_login: :property_manager do
    visit maintenance_ticket_path(ticket)

    perform_enqueued_jobs do
      click_actions
      click_action_item 'Close'

      expect(page).to have_content(/closed/i)
    end

    open_email(vendor.email)

    expect(current_email).to have_content(/invoice/i)
  end

  describe 'creating an invoice for an assignment' do
    include_context 'invoice filling'

    scenario 'a vendor creates an invoice for an assignment' do
      allow_any_instance_of(MaintenanceTicket).to receive(:may_resolve?).and_return(false)
      allow_any_instance_of(MaintenanceTicket).to receive(:resolved?).and_return(true)

      visit vendor_assignment_path(assignment.token)

      click_on 'Create Invoice'

      fill_in 'Date', with: Time.zone.today
      fill_in 'Invoice Number', with: (invoice_number = 'ACME-987654')
      fill_in 'Description', with: 'Exterior Painting'

      click_on 'Add Material'
      within '#section-material' do
        fill_in_vendor_line_item 0, description: 'Paint, per gallon',
                                    quantity: '3',
                                    unit_price: '24.99'
      end

      click_on 'Add Labor'
      within '#section-labor' do
        fill_in_vendor_line_item 0, description: 'Hourly Service',
                                    quantity: '2',
                                    unit_price: '35.50'
      end

      click_on 'Save'

      expect(page).to have_content(/invoice submitted successfully/i)

      expect(page).to have_content(invoice_number)

      invoice = assignment.reload.invoices.last!
      expect(invoice.amount).to eq(Monetize.parse('$145.97'))
      expect(invoice.invoice_number).to eq(invoice_number)
      expect(invoice.post_date).to eq Time.zone.today
      expect(invoice.linked_maintenance_tickets).to include(ticket)
      expect(invoice.buyer).to eq(property)
      expect(invoice.seller).to eq(vendor)

      chart = property.reload.company.chart_of_accounts
      expect(invoice.line_items.map(&:payable_account_id).uniq).to \
        eq([chart.default_maintenance_expense_account_id])

      # User notification
      notification = employee.notifications.last!
      expect(notification).to have_attributes(
        resource: ticket,
        title: "#{vendor.name} Submitted an Invoice",
        description: "Work Order ##{ticket.number}",
        kind: 'vendor_invoice_submitted'
      )
    end
  end
end
