class CreateCollectionsCommunications < ActiveRecord::Migration[7.0]
  def change
    create_table :collections_communications do |t|
      t.references :tenant, foreign_key: true, null: false
      t.integer :channel, null: false
      t.monetize :balance, currency: { present: false }, amount: { null: false }
      t.integer :status, null: false, default: 0
      t.datetime :processed_at, null: true

      t.timestamps
    end
  end
end
