#rbac-editor
  .ui.padded.basic.vertical.segment
    %h2.ui.header
      RBAC Permissions

    - RBAC::Permissions.all.each do |namespace, components|
      .ui.accordion
        .title 
          %i.dropdown.icon
          = namespace.to_s.titleize
        .content
          .transition
            .field
              .ui.checkbox
                %input{
                  type: :checkbox,
                  data: {
                    namespace: namespace,
                    role_form_target: "permissionNamespace",
                    action: 'change->role-form#togglePermissionNamespace'
                  }
                }
                %label
                  %b= "All #{namespace.to_s.titleize} Permissions"
            .ui.three.column.stackable.relaxed.padded.grid
              - components.each do |component, actions|
                .ui.column
                  .field
                    &emsp;
                    .ui.checkbox
                      %input{
                        type: :checkbox,
                        data: {
                          namespace: namespace,
                          component: component,
                          role_form_target: "permissionComponent",
                          action: 'change->role-form#togglePermissionComponent'
                        }
                      }
                      %label
                        %b= component.to_s.titleize
                      .grouped.fields
                        - actions.each do |action|
                          .field
                            &emsp;
                            .ui.checkbox
                              %input{
                                type: :checkbox,
                                name: "role[rbac_permissions][#{namespace}][#{component}][#{action}]",
                                multiple: true,
                                checked: role.permissions.any? { |p| p.namespace == namespace.to_s && p.component == component.to_s && p.action == action.to_s },
                                data: {
                                  namespace: namespace,
                                  component: component,
                                  role_form_target: "permissionAction"
                                }
                              }
                              %label
                                = action.titleize

:javascript
  $('.ui.accordion').accordion();
