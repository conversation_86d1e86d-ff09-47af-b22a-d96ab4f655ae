class DataExporting::CreateZipStream
  extend Service

  def initialize(klass:, target:, user:)
    @exporter = klass.new(target, user: user)
  end

  def call
    Zip::OutputStream.write_buffer do |zio|
      @exporter.call do |directory|
        Dir[File.join(directory, '**', '**')].each do |path|
          next if File.directory?(path)

          filename = path.gsub(directory + '/', '')

          zio.put_next_entry(filename)
          zio.write(File.read(path))
        end
      end
    end
  end
end
