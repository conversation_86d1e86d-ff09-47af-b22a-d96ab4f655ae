require 'swagger_helper'

require_relative 'rswag_context'

RSpec.describe 'api/v2/leases' do
  include_context 'rswag api v2'

  let!(:lease) do
    create(:lease, id: 1).tap do |lease|
      api_user.property_memberships.create!(target: lease.property)
    end
  end

  path '/api/v2/leases' do
    get('list leases') do
      response(200, 'successful') do
        run_test!
      end
    end
  end

  path '/api/v2/leases/{id}' do
    parameter name: :id, in: :path, type: :integer, description: 'id'

    get('show lease') do
      response(200, 'successful') do
        schema type: :object, properties: {
          id: { type: :integer },
          start_date: { type: :date },
          end_date: { type: :date }
        }

        let(:id) { lease.id }

        run_test!
      end
    end
  end
end
