.ui.top.attached.info.message
  .header
    Dates
  Please enter the date that the #{I18n.t('tenant_term').downcase.pluralize} moved out of the unit, as well as the
  date that you regained posession of the unit with all keys or other access methods returned."

= form_with model: @move_out,
  url: leasing_lease_move_out_path(@lease, @move_out),
  local: false,
  class: 'ui bottom attached clearing form segment' do |f|

  .field
    = f.label :move_out_date, 'Effective Move Out Date'
    = f.semantic_date_field :move_out_date

  .field
    = f.label :return_of_possession_date, 'Return of Possession Date'
    = f.semantic_date_field :return_of_possession_date

  .equal.width.fields{ data: { controller: 'leasing--move-outs--accounting-date' } }
    .field
      = f.label :accounting_date_setting do
        Accounting Date

        = render HelpTooltipComponent.new do |c|
          The Accounting Date is the date that move out accounting will take
          place, including the damages invoice, applied security deposit, and
          refund payable.

          - c.with_topic 'move outs',
            link: 'https://revela.helpjuice.com/managing-tenants/move-out-process'

      .ui.left.action.input{ data: { leasing__move_outs__accounting_date_target: 'input' } }
        = f.semantic_dropdown :accounting_date_setting,
          [['Move-Out Date', 'move_out_date'],
          ['Process Date', 'process_date'],
          ['Other Date', 'explicit_date']],
          {},
          class: 'ui fluid search selection dropdown',
          data: { leasing__move_outs__accounting_date_target: 'settingDropdown' }

        .field{ style: 'display: none;',
        data: { leasing__move_outs__accounting_date_target: 'explicitDate' } }
          = f.semantic_date_field :explicit_accounting_date

  .ui.error.message

  = link_to 'Back', nil, class: 'ui basic button'

  = f.submit 'Next', class: 'right floated ui button'
