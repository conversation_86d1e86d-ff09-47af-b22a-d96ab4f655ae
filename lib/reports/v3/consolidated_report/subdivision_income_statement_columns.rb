class Reports::V3::ConsolidatedReport::SubdivisionIncomeStatementColumns
  def initialize(entity:, period_start:, period_end:, subdivision: nil)
    @entity = entity
    @subdivision = subdivision
    @period_start = period_start
    @period_end = period_end
  end

  def columns
    groups = \
      entity
      .properties
      .reorder(subdivision: :asc)
      .group_by(&:subdivision)

    if subdivision
      [subdivision_column(subdivision, groups[subdivision])]
    else
      found_none = false

      columns = groups.map do |subdivision, properties|
        found_none = true if subdivision.nil?

        subdivision_column(subdivision, properties)
      end

      # Ensure a no subdivision column, even if there are no properties without
      # a subdivision
      columns << subdivision_column(nil, []) unless found_none

      columns
    end
  end

  private

  attr_reader :entity, :subdivision, :period_start, :period_end

  def subdivision_column(subdivision, properties)
    Reports::V3::ConsolidatedReport::SubdivisionColumn.new(
      name: subdivision || 'No Subdivision',
      entity: @entity,
      properties: properties,
      period_start: period_start,
      period_end: period_end
    )
  end
end
