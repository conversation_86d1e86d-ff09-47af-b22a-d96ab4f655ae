class Accounting::Context::Owner
  attr_reader :owner, :chart_of_accounts

  def initialize(owner)
    @owner = owner

    assign_single_chart_of_accounts_or_fail!
  end

  def as_json(options = {})
    {
      id: owner.to_sgid.to_s,
      name: name,
      type: '<PERSON><PERSON><PERSON>',
      child: 'En<PERSON><PERSON>',
      chart_of_accounts_id: configuration.chart_of_accounts_id
    }
  end

  def query_params
    { journal_id: owner.company_ids }
  end

  def children
    owner.companies.order(name: :asc)
  end

  def accounts
    Plutus::AccountsQuery.for_owner(owner)
  end

  delegate :name, to: :owner

  private

  def assign_single_chart_of_accounts_or_fail!
    charts = ChartsOfAccountsQuery.new.search.by_owner(owner)

    fail "Found #{charts.size} chart(s) of accounts" unless charts.size == 1

    @chart_of_accounts = charts.first
  end
end
