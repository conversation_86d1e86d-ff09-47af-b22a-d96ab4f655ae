module Messaging::Email::RelatedProperties
  # For use when deciding if some property manager is interested / has access
  # to this message based on related properties
  def related_properties
    properties_from_regarding || properties_from_sender || []
  end

  private

  def properties_from_regarding
    return nil unless regarding.is_a?(MaintenanceTicket)

    [regarding.property].compact.presence
  end

  def properties_from_sender
    return nil unless sender.is_a?(Tenant) || sender.is_a?(Owner)

    sender.properties.presence
  end
end
