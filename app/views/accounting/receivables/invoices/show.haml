= render ActionSidebar::ContainerComponent.new do |c|
  - c.with_main do
    = render 'flash_notices'

    .ui.two.column.grid
      .column
        .ui.breadcrumb
          .section
            = link_to 'Receivables', accounting_receivables_path
          %i.right.angle.icon.divider
          %h1.ui.header
            = page_title @invoice
            .sub.header
              Receivable Invoice
              .ui.tiny.label{ class: @invoice.status_color }
                = @invoice.status
      .column
        = ActionsMenu::Invoice::Receivable.(@invoice, current_property_manager, @terminal)
        - require_permission :print_accounting_receivables? do
          = print_button

    = render partial: 'shared/invoice', locals: { invoice: @invoice }

    - if Feature.enabled?(:payables_pdf_rendering, Customer.current) && @invoice.pdf_attachment
      = inline_pdf pdf_attachment_accounting_payables_invoice_path(@invoice)

      :css
        .pdf.paper {
          max-width: 900px !important;
        }

    = render partial: 'management/audit_logs/loader',
      locals: { auditable: @invoice }

  - unless current_property_manager.role.name.in?(['Chapter Advisor / Officer', 'House Director']) && CustomerSpecific::Behavior.disable_based_on_role_name?
    - c.with_sidebar do |s|
      = render partial: 'shared/invoice/sidebar', locals: { s: s, invoice: @invoice, can_add_recurring: current_property_manager.can.add_recurring_accounting_receivables? }

= render Accounting::Invoices::MarkPaidModalComponent.new(invoice: @invoice)

= render partial: 'shared/accounting/waive_modal',
  locals: { invoice: @invoice }

= render Accounting::Receivables::TerminalModalComponent.new(terminal: @terminal)
