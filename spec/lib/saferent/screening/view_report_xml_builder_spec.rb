require 'rails_helper'

RSpec.describe Saferent::Screening::ViewReportXmlBuilder do
  before do
    create(:saferent_credentials)
  end

  let(:lease_term) { create(:lease_term) }
  let!(:lease_application) do
    create(:lease_application, :safe_rent_screening, lease_term: lease_term)
  end
  let!(:saferent_screening) do
    create(
      :saferent_screening,
      :deluxe,
      lease_application: lease_application,
      transaction_number: '12345'
    )
  end

  let!(:xml) do
    Nokogiri::Slop(described_class.new(lease_application).to_xml)
  end

  let!(:request) { xml.ApplicantScreening }

  describe 'with requestType' do
    it 'sets a specific value' do
      request_type = 'ViewPDF'

      xml_request_type = request.Request.RequestType.content
      expect(xml_request_type).to eq(request_type)
    end
  end

  describe 'with reportId' do
    it 'accepts a custom value' do
      report_id = saferent_screening.transaction_number
      xml_report_id = request.Request.ReportID.content
      expect(xml_report_id).to eq(report_id)
    end
  end

  describe 'with applicantId' do
    def applicant_id(applicant)
      applicant.AS_Information.ApplicantIdentifier.content
    end

    it 'has the correct applicantIds' do
      applicants = request.Applicant
      expect(applicant_id(applicants[0])).to \
        eq(lease_application.lease_application_memberships.first.id.to_s)

      expect(applicant_id(applicants[1])).to \
        eq(lease_application.lease_application_memberships.second.id.to_s)
    end
  end
end
