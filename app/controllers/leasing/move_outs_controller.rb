class Leasing::MoveOutsController < LeasingController
  before_action :set_lease
  before_action :set_pending_move_out, except: %i[new create reopen]
  before_action :set_processed_move_out, only: :reopen

  layout 'move_out'

  def new
    @move_out = @lease.move_outs.build
  end

  def dates
    return unless @lease.on_notice?

    @move_out.move_out_date ||= \
      @lease.notice_of_non_renewal.anticipated_move_out_date
  end

  def destroy
    if @move_out.processed?
      flash[:error] = 'Cannot delete a processed move out.'
    else
      @move_out.destroy!
      flash[:success] = 'Move out canceled.'
    end

    redirect_to leasing_lease_path(@lease)
  end

  def create
    @move_out = @lease.move_outs.build(move_out_params)

    @move_out.termination_reason = @lease.notice_of_non_renewal&.reason

    if @move_out.save
      redirect_to dates_leasing_lease_move_out_path(@lease, @move_out)
    else
      flash.now[:error] = @move_out.errors.full_messages
      render :new, status: :unprocessable_entity
    end
  end

  def update
    result = nil

    ActiveRecord::Base.transaction do
      if params.dig(:lease_move_out, :perform_rekey) == '1'
        assignee_sgid = params.dig(:lease_move_out, :rekey_assignee)
        assignee = GlobalID::Locator.locate_signed(assignee_sgid)
        subject = params.dig(:lease_move_out, :rekey_subject)

        result = Lease::MoveOut::CreateRekey.call(
          move_out: @move_out,
          user: current_property_manager,
          assignee:,
          subject:
        )

        fail ActiveRecord::Rollback unless result.successful?
      end

      if params.dig(:lease_move_out, :perform_inspection) == '1'
        assigned_to_sgid = params.dig(:lease_move_out, :inspection_assignee)
        assigned_to = GlobalID::Locator.locate_signed(assigned_to_sgid)
        template_id = params.dig(:lease_move_out, :inspection_template_id)

        result = Lease::MoveOut::CreateInspection.call(
          move_out: @move_out,
          user: current_property_manager,
          assigned_to:,
          template_id:
        )

        fail ActiveRecord::Rollback unless result.successful?
      end

      @move_out.update!(move_out_params)

      result = OpenStruct.new(successful?: true)
    end

    return render_ujs_errors result.errors unless result.successful?

    path = Rails.application.routes.recognize_path(request.referer)
    action = case path[:action]
             when 'dates'
               'termination'
             when 'termination'
               'forwarding_addresses'
             when 'forwarding_addresses'
               'assessment'
             when 'assessment'
               'damages'
             when 'damages'
               'review'
             else
               'dates'
             end

    redirect_to send(
      "#{action}_leasing_lease_move_out_path",
      @lease,
      @move_out
    )
  rescue ActiveRecord::RecordInvalid => e
    render_ujs_errors e.record.errors.full_messages
  end

  def forwarding_addresses
    @move_out.memberships.each do |membership|
      next if membership.address

      if (existing = membership.tenant.forwarding_address)
        membership.address = existing.dup
      else
        membership.build_address
      end
    end
  end

  def damages
    @damage_presets = configuration.charge_presets.damage

    @damage_accounts = Plutus::AccountsQuery
                       .new.search
                       .order(gl_code: :asc, name: :asc)
                       .by_company(property.company)
                       .receivable
  end

  def execute
    review_params = params.fetch(:lease_move_out, {})
                          .permit(:cancel_scheduled_payments)

    @move_out.assign_attributes(review_params)

    result = Lease::MoveOut::Process.call(move_out: @move_out)

    if result.successful?
      flash[:success] = 'Move Out Processed Successfully'
      redirect_to_itemized_damages(@move_out)
    else
      render_ujs_errors result.errors
    end
  end

  def reopen
    result = Lease::MoveOut::Reopen.call(move_out: @move_out)

    if result.successful?
      flash[:success] = 'Move Out Reopened Successfully'
      redirect_to review_leasing_lease_move_out_path(@lease, @move_out)
    else
      flash[:error] = result.errors
      redirect_to_itemized_damages(@move_out)
    end
  end

  private

  def set_lease
    @lease = LeasesQuery
             .for_user(current_property_manager)
             .includes(lease_memberships: :tenant)
             .find(params[:lease_id])
  end

  def set_pending_move_out
    @move_out = move_outs.pending.find(params[:id])
  end

  def set_processed_move_out
    @move_out = move_outs.processed.find(params[:id])
  end

  def move_out_params
    # TODO: rails is adding 0 to checkbox arrays
    if params.dig(:lease_move_out, :lease_membership_ids)
      params[:lease_move_out][:lease_membership_ids].reject! do |id|
        id == '0'
      end
    end

    params.require(:lease_move_out)
          .permit(:move_out_date, :return_of_possession_date,
                  :accounting_date_setting, :explicit_accounting_date,
                  :walk_through_date, :walk_through_summary,
                  :terminate_lease, :termination_date,
                  :termination_reason, :termination_description,
                  :cancel_scheduled_payments,
                  lease_membership_ids: [],
                  memberships_attributes: [
                    :id, address_attributes: Address::PERMISSABLE_ATTRIBUTES
                  ])
  end

  def redirect_to_itemized_damages(move_out)
    redirect_to leasing_lease_itemized_damage_path(
      move_out.lease, move_out.itemized_damages
    )
  end

  delegate :property, :move_outs, to: :@lease
  delegate :configuration, to: :property
end
