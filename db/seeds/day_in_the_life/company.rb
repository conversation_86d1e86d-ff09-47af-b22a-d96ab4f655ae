DayInTheLife.define :company do
  before do
    require_relative '../mckinley.rb'

    subdomain = COMPANY.fetch(:subdomain, 'alever')

    customer = FactoryBot.create(:customer, name: COMPANY[:name],
                                            subdomain: subdomain)
    customer.create_schema!
    customer.activate!
    company = FactoryBot.create(:company, customer: customer,
                                          name: customer.name)

    # Employees
    [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ].each do |email|
      user = FactoryBot.create(:property_manager,
                               company: company,
                               email: email)
      PropertyMembership.create(target: company, user: user)
    end

    COMPANY[:groups].each do |group_data|
      # Group
      group = FactoryBot.create(:property_group,
                                name: group_data[:name],
                                company: company)

      # Owner
      if group_data[:owner]
        first_name, last_name = group_data[:owner]&.split(' ')
        options = { company: company }
        options[:first_name] = first_name if first_name
        options[:last_name] = last_name if last_name
        owner = FactoryBot.create(:owner, options)

        PropertyMembership.create(target: group, user: owner)
      end

      # Properties
      group_data[:properties].each do |property_data|
        name = property_data[:name] || property_data[:line_one]
        # Property
        property = FactoryBot.build(:property,
                                    company: company,
                                    kind: property_data[:kind],
                                    name: name)

        # Address
        property.build_address(line_one: property_data[:line_one],
                               city: property_data[:city],
                               region: property_data[:region],
                               postal_code: property_data[:postal_code],
                               country: 'United States')

        property.save!

        # Group Membership
        PropertyGroupMembership.create(property: property, property_group: group)

        # Units
        unit_names = property_data[:units] ||
          Array.new(property_data[:unit_count]&.to_i || 1) { |i| "Unit #{i + 1}" }

        floorplans = (property_data[:floorplans] || []).map do |fp_name|
          FactoryBot.create(:floorplan, property: property, name: fp_name)
        end

        unit_names.each do |unit_name|
          floorplan = floorplans.sample || FactoryBot.create(:floorplan, property: property)

          FactoryBot.create(:unit,
                            property: property,
                            name: unit_name,
                            floorplan: floorplan)
        end
      end
    end
  end
end
