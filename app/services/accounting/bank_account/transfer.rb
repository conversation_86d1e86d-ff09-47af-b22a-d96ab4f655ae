class Accounting::BankAccount::Transfer
  extend ::Service

  def initialize(
    withdraw_account:,
    deposit_account:,
    amount:,
    date:,
    description:
  )
    @withdraw_account = withdraw_account
    @deposit_account = deposit_account
    @amount = amount
    @date = date
    @description = description
  end

  attr_reader :withdraw_account, :deposit_account, :amount, :date, :description

  def call
    unless amount.positive?
      return OpenStruct.new(successful?: false,
                            errors: ['Amount must be positive'])
    end

    OpenStruct.new(successful?: true, entries: make_entries)
  rescue ActiveRecord::RecordInvalid => e
    OpenStruct.new(successful?: false, errors: e.record.errors.full_messages)
  end

  private

  def cross_journal?
    withdraw_account.owner != deposit_account.owner
  end

  def make_entries
    if cross_journal?
      ActiveRecord::Base.transaction do
        entry_for_local_journal = make_entry_for_local_journal
        entry_for_remote_journal = make_entry_for_remote_journal

        Plutus::Entry::Linkage.create!(
          left_entry: entry_for_local_journal,
          right_entry: entry_for_remote_journal
        )

        [entry_for_local_journal, entry_for_remote_journal]
      end
    else
      [make_entry_for_single_journal]
    end
  end

  def make_entry_for_local_journal
    debit_account = Accounting::DueToFromMatrix.new(
      withdraw_account.owner
    ).due_to(
      deposit_account.owner
    )

    credit_account = withdraw_account.ledger_account

    create_journal_entry(withdraw_account, debit_account, credit_account)
  end

  def make_entry_for_remote_journal
    debit_account = deposit_account.ledger_account

    credit_account = Accounting::DueToFromMatrix.new(
      deposit_account.owner
    ).due_from(
      withdraw_account.owner
    )

    create_journal_entry(deposit_account, debit_account, credit_account)
  end

  def make_entry_for_single_journal
    debit_account = deposit_account.ledger_account
    credit_account = withdraw_account.ledger_account

    create_journal_entry(withdraw_account, debit_account, credit_account)
  end

  def create_journal_entry(bank_account, debit_account, credit_account)
    bank_account.owner.journal_entries.create!(
      kind: :manual_entry,
      date: date,
      description: description,
      debits: [{ account: debit_account, amount: amount.cents }],
      credits: [{ account: credit_account, amount: amount.cents }]
    )
  end
end
