class ApplicationMailer < ActionMailer::Base
  include EmailRecording

  TERMS_URL = 'https://revela-public.s3.amazonaws.com/terms.pdf'.freeze
  PRIVACY_URL = 'https://revela-public.s3.amazonaws.com/privacy.pdf'.freeze
  EQUAL_HOUSING_LOGO_URL = 'https://revela-public.s3.amazonaws.com/equal_housing.png'.freeze
  BILLING_EMAIL = 'Revela Billing <<EMAIL>>'.freeze
  SUPPORT_EMAIL = 'Revela Support <<EMAIL>>'.freeze
  NOTIFICATIONS_EMAIL = 'Revela Notifications <<EMAIL>>'.freeze
  PHONE_NUMBER = '(*************'.freeze

  default from: SUPPORT_EMAIL

  default 'Message-ID' => lambda { |mailer|
    domain = mailer.smtp_settings.fetch(:domain)

    "<#{SecureRandom.uuid}@#{domain}>"
  }

  layout 'mailer'

  before_action { @brand = Customer.current&.brand }
end
