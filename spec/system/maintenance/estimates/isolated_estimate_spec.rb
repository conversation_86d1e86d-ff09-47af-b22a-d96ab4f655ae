require 'rails_helper'

RSpec.describe 'isolated estimates', :elasticsearch, :js,
               capybara_login: :property_manager do
  let!(:client_entity) { Customer.current.client_entity }

  let!(:service_area) do
    perform_enqueued_jobs { create(:property) }.tap do
      Property.__elasticsearch__.refresh_index!
    end
  end

  scenario 'a user creates an isolated estimate' do
    visit maintenance_estimates_path

    click_on 'New Estimate'

    search_for service_area.name, in: 'Service Area'

    fill_in 'Summary', with: (summary = 'My Summary')

    fill_in 'Section', with: (section_name = 'My Section')
    fill_in 'Work Area', with: (area_name = 'My Area')
    fill_in 'Press enter to add more tasks', with: (task_body = 'My Task')

    click_on 'Save'

    expect(page).to have_content(/estimate prepared successfully/i)

    estimate = Maintenance::Estimate.last!
    expect(estimate.summary).to eq(summary)
    expect(estimate.service_area).to eq(service_area)
    expect(estimate.bill_to).to eq(service_area.company)
    expect(estimate.bill_from).to eq(client_entity)

    section = estimate.sections.first!
    expect(section.name).to eq(section_name)

    area = section.areas.first!
    expect(area.name).to eq(area_name)

    task = area.tasks.first!
    expect(task.body).to eq(task_body)
  end
end
