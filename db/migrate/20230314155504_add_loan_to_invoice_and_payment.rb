class AddLoanToInvoiceAndPayment < ActiveRecord::Migration[6.1]
  def change
    %i[invoices payments].each do |table|
      add_reference table, :loan, foreign_key: { to_table: :lending_loans }, null: true
    end

    return unless Customer.current_subdomain == 'cod'

    # Associate existing loans, to retain Accounting::Ledger::Loan behavior
    Lending::Loan.find_each do |loan|
      [Invoice, Payment].each do |klass|
        klass.where(
          'description ILIKE ?', "%#{loan.loan_number}%"
        ).each do |document|
          document.update_columns(loan_id: loan.id)
        end
      end
    end
  end
end
