##
# A base class to extend to access the Nelco SOAP operations through #client.
class Taxes::Nelco::Service
  extend Service

  private

  def client
    @client ||= Savon.client(wsdl: wsdl_url)
  end

  def wsdl_url
    if Rails.env.production? && !Taxes::Nelco.pats?
      'https://www.wagefiler.com/WageFilerWS/wagefiler.asmx?WSDL'
    else
      'https://sdkdev.wagefiler.com/WageFilerWS/wagefiler.asmx?WSDL'
    end
  end
end
