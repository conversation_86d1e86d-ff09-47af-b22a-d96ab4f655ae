require 'rails_helper'

RSpec.describe Collections::Eviction::BulkAfterCreateJob do
  let(:evictions) { create_list(:collections_eviction, 3) }

  it 'enqueues a job for each eviction' do
    perform_enqueued_jobs(only: described_class) do
      described_class.perform_later(evictions.map(&:id))
    end

    expect(Collections::Eviction::AfterCreateJob).to \
      have_been_enqueued.exactly(evictions.count).times
  end
end
