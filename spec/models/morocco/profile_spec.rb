require 'rails_helper'

RSpec.describe Morocco::Profile do
  subject(:profile) { build(:morocco_profile) }

  describe 'factory' do
    it { is_expected.to be_valid }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:phone) }

    describe 'on uniqueness of phone' do
      it 'sets a validation error when duplicated' do
        create(:morocco_profile)
        subject.save
        expect(subject.errors[:phone].first).to match(/has already been taken/)
      end
    end
  end
end
