class Api::V2::AddressResource < JSONAPI::Resource
  include Api::V2::Shared

  attributes :line_one, :line_two, :city, :region, :postal_code, :country
  attribute :display
  attributes :latitude, :longitude
  attributes :created_at, :updated_at

  def display
    @model.simple_address
  end

  def self.user_records(relation, user)
    user_addresses = Address.where(addressable: user.properties)

    relation.where(id: user_addresses.select(:id))
  end
end
