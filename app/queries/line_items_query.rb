class LineItemsQuery
  def initialize(relation = LineItem.all)
    @relation = relation.extending(Scopes)
  end

  def search
    @relation
  end

  module Scopes
    def late_by_preset_on_date(preset:, date: Time.zone.today)
      target_due_date = date.yesterday - preset.grace_period

      charge_presets = charge_presets_using(preset: preset)

      invoices = InvoicesQuery
                 .new.search
                 .receivable
                 .where(due_date: target_due_date)
                 .open(date.yesterday)
                 .without_payment_plan(date.yesterday)

      LineItem
        .where(invoice: invoices)
        .where(charge_preset: charge_presets)
    end

    def with_priority
      primary_rent_account_ids =
        ChargePreset
        .rent.pluck(:account_id).join(',').presence || 'NULL'

      secondary_rent_account_ids =
        Plutus::Account
        .where(category: 'Rent Income')
        .pluck(:id).join(',').presence || 'NULL'

      select('line_items.*')
        .select("
          (
            CASE
              WHEN
                line_items.receivable_account_id
                IN (#{primary_rent_account_ids})
                THEN 2
              WHEN
                line_items.receivable_account_id
                IN (#{secondary_rent_account_ids})
                THEN 1
              ELSE 0
            END
          ) AS priority
      ")
    end

    private

    def charge_presets_using(preset:)
      ChargePreset
        .joins(:late_fees)
        .where(charge_presets_late_fees: { late_fee: preset })
    end
  end
end
