require 'rails_helper'

RSpec.describe Invoice::Waive do
  let(:fifty_dollars) { Monetize.parse('$50.00') }
  let(:one_hundred_dollars) { Monetize.parse('$100.00') }

  let(:payable_account) { create(:expense_account) }
  let(:receivable_account) { create(:revenue_account) }

  let(:invoice) do
    create(:management_fee_invoice, amount: one_hundred_dollars)
  end

  describe '#call' do
    subject(:result) { described_class.call(invoice) }

    context 'a partially paid invoice' do
      before do
        create(:invoice_payment, invoice: invoice, amount: fifty_dollars)
        invoice.reload
      end

      it { is_expected.not_to be_successful }
    end

    context 'a future invoice' do
      before do
        invoice.update!(date: Time.zone.today + 3.days,
                        post_date: Time.zone.today + 3.days,
                        due_date: Time.zone.today + 33.days)
      end

      it { is_expected.not_to be_successful }
    end

    context 'an invoice for a rent on a lease with an eviction' do
      let(:invoice) { create(:rent_invoice) }
      let(:lease) { invoice.buyer_lease_membership.lease }

      before do
        create(:collections_eviction, lease: lease)
        lease.configuration.update!(
          prevent_electronic_payments_while_evicting: true
        )
      end

      it { is_expected.not_to be_successful }

      it {
        is_expected.to have_attributes errors: [a_string_matching('eviction')]
      }
    end

    context 'an unpaid invoice' do
      it { is_expected.to be_successful }

      it 'causes invoice balance to be zero' do
        expect(result).to be_successful
        expect(invoice.reload.balance).to eq Money.zero
        expect(invoice.balance(Time.zone.now)).to eq Money.zero
        expect(invoice.balance(1.day.ago)).to eq(invoice.amount)
      end

      describe 'the invoice' do
        subject!(:result_invoice) { result.invoice }

        it { is_expected.not_to be_paid }

        it { is_expected.not_to be_open }

        it { is_expected.to be_waived }

        its(:waived_at) { is_expected.to eq(Time.zone.today) }

        it 'is included in the waived invoices scope' do
          expect(InvoicesQuery.new.search.waived).to include(result_invoice)
        end

        it 'is included in the closed invoices scope' do
          expect(InvoicesQuery.new.search.closed).to include(result_invoice)
        end

        it 'is not included in the open invoices scope' do
          expect(InvoicesQuery.new.search.open).not_to include(result_invoice)
        end

        it 'is not included in the paid invoices scope' do
          expect(InvoicesQuery.new.search.paid).not_to include(result_invoice)
        end

        it 'is included in the unpaid invoices scope' do
          expect(InvoicesQuery.new.search.unpaid).to include(result_invoice)
        end
      end

      describe 'the receivable journal entry' do
        subject(:entry) do
          result.invoice.journal_entries.where(journal: invoice.seller).last
        end

        its(:description) { is_expected.to match(/waive/i) }

        describe 'the debit' do
          subject(:debit) { entry.debit_amounts.first }

          its(:amount) { is_expected.to eq(one_hundred_dollars.cents) }

          describe 'the account' do
            subject(:account) { debit.account }

            its(:name) { is_expected.to eq('Bad Debt Expense') }
          end
        end

        describe 'the credit' do
          subject(:credit) { entry.credit_amounts.first }

          its(:amount) { is_expected.to eq(one_hundred_dollars.cents) }

          describe 'the account' do
            subject(:account) { credit.account }

            its(:name) { is_expected.to eq('Accounts Receivable') }
          end
        end
      end

      describe 'the payable journal entry' do
        subject(:entry) do
          result.invoice.journal_entries.where(journal: invoice.buyer).last
        end

        its(:description) { is_expected.to match(/waive/i) }

        describe 'the debit' do
          subject(:debit) { entry.debit_amounts.first }

          its(:amount) { is_expected.to eq(one_hundred_dollars.cents) }

          describe 'the account' do
            subject(:account) { debit.account }

            its(:name) { is_expected.to eq('Accounts Payable') }
          end
        end

        describe 'the credit' do
          subject(:credit) { entry.credit_amounts.first }

          its(:amount) { is_expected.to eq(one_hundred_dollars.cents) }

          describe 'the account' do
            subject(:account) { credit.account }

            it { is_expected.to eq(entry.commercial_document.line_items.first.payable_account) }
          end
        end
      end
    end
  end
end
