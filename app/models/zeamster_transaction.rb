class ZeamsterTransaction < ApplicationRecord
  include Zeamster::Transaction::ReasonCodes

  belongs_to :payment

  validates :payment, presence: true
  validates :zeamster_id, presence: true

  audited except: :last_refreshed_at, associated_with: :payment

  enum :status, {
    approved: 101,
    auth_only: 102,
    refunded: 111,
    avs_only: 121,
    pending_origination: 131,
    originating: 132,
    originated: 133,
    settled: 134,
    voided: 201,
    declined: 301,
    charged_back: 331
  }

  def merchant_account
    MerchantAccount.find_by!(location_id: location_id)
  end

  def status_text
    reason_short_description
  end

  def status_summary
    reason_description
  end

  alias_attribute :reference_number, :zeamster_id
end
