class OwnersQuery
  def initialize(relation = Owner.all)
    @relation = relation.extending(Scopes)
  end

  def search
    @relation
  end

  def self.for_user(user)
    new.search.by_user(user)
  end

  module Scopes
    include TagFiltering

    def by_user(user)
      return self if user.try(:top_level?)

      return [user] if user.is_a?(Owner)

      where(
        id: Ownership.where(entity: user.companies).select(:owner_id)
      )
    end

    def by_company(company)
      company.owners
    end

    def by_property(property)
      property.company.owners
    end

    def filter_kind(kind)
      return self unless kind

      where(kind: kind)
    end

    def filter_email(email)
      return self unless email

      where('email ILIKE ?', "%#{email}%")
    end

    def filter_name(name)
      return self unless name

      where("((first_name || ' ' || last_name) ILIKE ?)", "%#{name}%")
    end

    def filter_text(text)
      return self if text.blank?

      filter_name(text).or(filter_email(text))
    end

    def order_with(params, default_params: { column: 'last_name' })
      sort = params || default_params

      return self if sort.blank? || sort[:column].blank?

      direction = sort[:direction] == 'descending' ? 'DESC' : 'ASC'

      case sort[:column]
      when 'last_name', 'phone', 'email'
        reorder(sort[:column] => direction)
      else
        self
      end
    end
  end
end
