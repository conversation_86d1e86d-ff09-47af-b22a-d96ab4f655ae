class Lease::NoticeToQuit
  extend Service
  include PdfFilling

  attr_reader :params, :lease, :tenant, :user, :generate_only, :demand_letter

  def initialize(params:, lease:, tenant:, user:, generate_only: false, demand_letter: nil)
    @params = params
    @lease = lease
    @tenant = tenant
    @user = user
    @generate_only = generate_only
    @demand_letter = demand_letter
  end

  def call
    if unit.archived?
      return OpenStruct.new(successful?: false, errors: ['Unit is archived'])
    end

    if landlord.address.blank?
      return OpenStruct.new(successful?: false,
                            errors: ['Landlord must have an address'])
    end

    region_code = lease.address.region_code

    if notice_type == 'notice_to_quit'
      unless region_code == 'MI'
        return OpenStruct.new(
          successful?: false, errors: [
            "Notice To Quit Not Configured for #{region_code}"
          ]
        )
      end
    elsif notice_type == 'demand_for_possession'
      unless Collections::DemandLetter::ConfiguredTemplate.exists?(
        address: lease.address
      )
        return OpenStruct.new(
          successful?: false, errors: [
            "Demand Letter Not Configured for #{region_code}"
          ]
        )
      end
    end

    if generate_only
      file = generate_pdf(output_file: output_file)

      OpenStruct.new(successful?: true, file: file)
    else
      generate_pdf(output_file: output_file) do |pdf_file|
        document = InvoiceAttachment.create!(
          parent: tenant,
          upload: pdf_file
        )

        Contact::TimelineEntry.create!(
          author: user,
          unit: unit,
          regarding: tenant,
          kind: notice_type,
          body: "#{notice_type.titleize} Generated"
        )

        if electronic_delivery?
          EvictionsMailer
            .send(notice_type, tenant, lease, document, balance.cents)
            .deliver_later
        end
      end

      OpenStruct.new(successful?: true)
    end
  rescue ActiveRecord::RecordInvalid => e
    OpenStruct.new(successful?: false, errors: e.record.errors.full_messages)
  end

  protected

  def pdf_values
    {
      'recipient' => recipient,
      'landlord_name' => landlord_name,
      'landlord_address' => landlord_address,
      'landlord_city_state_zip' => landlord_city_state_zip,
      'landlord_phone' => landlord_phone,
      'premises_address' => premises_address,
      'move_out_date' => move_out_date,
      'balance_owed' => balance_owed,
      'date' => date,
      'delivery_first_class_mail' => electronic_delivery? ? 'Off' : 'On',
      'delivery_electronic_service' => electronic_delivery? ? 'On' : 'Off',
      'electronic_service_address' => electronic_delivery? ? email : nil,
      'mcl_5541341' => lease_expired? ? 'On' : 'Off',
      'other' => lease_expired? ? 'Off' : 'On',
      'other_description' => lease_expired? ? nil : reason,
      'service_date' => electronic_delivery? ? date : nil,
      'service_name' => electronic_delivery? ? tenant.name : nil,
      'service_signature' => electronic_delivery? ? service_signature : nil,
      'agent_signature' => electronic_delivery? ? agent_signature : nil
    }
  end

  def notice_type
    'notice_to_quit'
  end

  def pdf_template_path
    Rails.root.join('spec', 'fixtures', "#{notice_type}.pdf")
  end

  def output_file
    dir = Rails.root.join \
      'tmp', 'pdfs', notice_type, SecureRandom.uuid
    FileUtils.mkpath dir
    path = dir.join("#{notice_type}.pdf")
    File.open(path, 'w+')
  end

  private

  def recipient
    [
      financially_responsible_recipients,
      unit.address.line_one,
      unit.address.line_two,
      unit.address.city_state_zip
    ].compact_blank.uniq.join("\n")
  end

  def financially_responsible_recipients
    tenants = lease.lease_memberships
                   .select(&:financially_responsible?)
                   .map(&:tenant)

    names = tenants.map(&:name)

    names << 'all other occupants'

    names.to_sentence
  end

  def date
    Time.zone.today.strftime('%B %-d, %Y')
  end

  def landlord_name
    landlord.name
  end

  def landlord_address
    landlord.address.street_address
  end

  def landlord_city_state_zip
    landlord.address.city_state_zip
  end

  def landlord_phone
    if Customer.current.subdomain == 'shamrock'
      '(*************'
    else
      landlord.formatted_phone
    end
  end

  def premises_address
    address = unit.address

    [
      address.line_one,
      address.line_two,
      address.city_state_zip
    ].compact_blank.uniq.join(', ')
  end

  def balance
    @balance ||= @demand_letter&.overdue_balance || lease.chain.aging_delinquency.overdue
  end

  def balance_owed
    balance.format(symbol: nil)
  end

  def move_out_date
    Date.parse(params[:eviction][:move_out_date])&.strftime('%B %-d, %Y')
  end

  def lease_expired?
    params[:eviction][:lease_expired] == '1'
  end

  def reason
    params[:eviction][:reason_for_termination]
  end

  def electronic_delivery?
    params[:eviction][:electronic_delivery] == '1'
  end

  def landlord
    if Customer.current.subdomain.start_with?('gebraelmgmt', 'ag-mgmt', 'senihmgt', 'marketplacehomes')
      Customer.current.client_entity
    else
      property.company
    end
  end

  def agent
    landlord.representative
  end

  def agent_signature
    "/s/ #{agent.name}"
  end

  def service_signature
    "/s/ #{user.name}"
  end

  delegate :unit, :property, to: :lease
  delegate :email, to: :tenant
end
