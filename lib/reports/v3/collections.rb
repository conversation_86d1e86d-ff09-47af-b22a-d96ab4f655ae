class Reports::V3::Collections < Reports::V3::Report
  MAPPING = {
    original_creditor: 'Original Creditor',
    original_account_id: 'Original Account ID',
    account_id: 'Account ID',
    transaction_id: 'Transaction ID',
    service_product: 'Service / Product',
    member_last_name: 'Member Last Name',
    member_first_name: 'Member First Name',
    member_address_street_address: 'Member Address',
    member_address_city: 'Member City',
    member_address_region: 'Member State',
    member_address_postal_code: 'Member Zip Code',
    member_ssn: 'Member SSN',
    member_date_of_birth: 'Member DOB',
    member_home_phone: 'Member Home Phone',
    member_phone: 'Member Cell Phone',
    member_email: 'Member Email Address',
    member_email_consent: 'Do you have contractual consent to communicate with this member via email?',
    member_sms_consent: 'Do you have contractual consent to communicate with this member via text message?',
    co_applicant_last_name: 'Co-applicant Last Name',
    co_applicant_first_name: 'Co-applicant First Name',
    co_applicant_address_street_address: 'Co-applicant Address',
    co_applicant_address_city: 'Co-applicant City',
    co_applicant_address_region: 'Co-applicant State',
    co_applicant_address_postal_code: 'Co-applicant Zip Code',
    co_applicant_ssn: 'Co-applicant SSN',
    co_applicant_date_of_birth: 'Co-applicant DOB',
    co_applicant_home_phone: 'Co-applicant Home Phone',
    co_applicant_work_phone: 'Co-applicant Work Phone',
    co_applicant_phone: 'Co-applicant Cell Phone',
    co_applicant_email: 'Co-applicant Email Address',
    co_applicant_email_consent: 'Do you have contractual consent to communicate with this Co-applicant via email?',
    co_applicant_sms_consent: 'Do you have contractual consent to communicate with this Co-applicant via text message?',
    charge_off_date: 'Charge-off Date',
    date_of_last_charge: 'Date of Last Charge',
    date_of_last_payment: 'Date of Last Payment',
    last_payment_amount: 'Last Payment Amount',
    total_due_at_charge_off: 'Total due at Charge-off',
    total_fees: 'Total of Fees',
    total_interest: 'Total of Interest',
    total_credits: 'Total Credits / Payments',
    referred_balance: 'Referred Balance (sum of total due at charge-off, fees, interest, credits, and payments)'
  }.freeze

  def self.available_filters
    %i[property]
  end

  def mapping
    MAPPING
  end

  def scope
    Reports::V3::Scope.new(user: user, filters: filters, fallback: :property)
  end

  def basis
    Class.new(Reports::V3::Basis::Base) do
      def relation
        Agreements::SimpleAgreement
          .all
          .joins(:company)
          .joins(:property)
          .includes(
            tenants: %i[forwarding_address taxpayer_identification],
            company: :portfolio
          )
          .joins(:primary_tenant)
          .merge(Company.order(name: :asc))
          .merge(Tenant.order(last_name: :asc))
      end

      def scoped(query)
        scope.apply(query) { |s| where(property: s.properties) }
      end

      def source_rows
        super
          .map { |row| Reports::V3::Collections::Row.new(row) }
          .select(&:visible?)
      end

      MAPPING.each_key do |key|
        column key do
          { value: send(key) }
        end
      end
    end
  end

  class Row
    def initialize(agreement)
      @agreement = agreement
      @primary_tenant = agreement.tenants.first
      @co_applicant = agreement.tenants.last
    end

    def respond_to_missing?(method)
      MAPPING.key?(method)
    end

    def original_creditor
      agreement.company.name
    end

    def original_account_id
      agreement.id
    end

    def member_phone
      member.formatted_phone
    end

    def co_applicant_phone
      co_applicant.formatted_phone
    end

    def member_ssn
      member.tin
    rescue StandardError
      nil
    end

    def co_applicant_ssn
      co_applicant.tin
    rescue StandardError
      nil
    end

    def member_sms_consent
      member.agreed_to_sms_at.present? ? 'Y' : 'N'
    end

    def co_applicant_sms_consent
      co_applicant.agreed_to_sms_at.present? ? 'Y' : 'N'
    end

    def date_of_last_charge
      last_charge&.post_date
    end

    def date_of_last_payment
      last_payment&.date
    end

    def last_payment_amount
      last_payment&.amount
    end

    def method_missing(method)
      case method
      when /(member|co_applicant)_address_(.*)/
        send(Regexp.last_match(1)).billing_address&.send(Regexp.last_match(2))
      when /home_phone/, /work_phone/
        nil
      when /consent/
        'Y'
      when /(member|co_applicant)_(.*)/
        send(Regexp.last_match(1)).send(Regexp.last_match(2))
      end
    end

    def total_due_at_charge_off
      aged_balance
    end

    def total_fees
      Money.zero
    end

    def total_interest
      Money.zero
    end

    def total_credits
      Money.zero
    end

    def referred_balance
      total_due_at_charge_off + total_fees + total_interest - total_credits
    end

    def charge_off_date
      Date.new(2022, 6, 30)
    end

    def visible?
      member && co_applicant && referred_balance.positive?
    end

    private

    attr_reader :agreement, :primary_tenant, :co_applicant

    delegate :ledger, to: :agreement

    def member
      primary_tenant
    end

    def last_payment
      ledger.paid_payments.last
    end

    def last_charge
      ledger.payable_invoices.last
    end

    def aged_balance
      aged_invoice_balance - aged_credit_balance
    end

    def aged_invoice_balance
      invoices = \
        InvoicesQuery
        .new(ledger.payable_invoices)
        .search.open
        .select do |invoice|
          invoice.due_date <= target_aged_date
        end

      Money.sum(invoices.map(&:balance))
    end

    def aged_credit_balance
      credits = \
        PaymentsQuery
        .new(ledger.paid_payments).search.with_credit
        .reject(&:considered_for_prepayment?)
        .select { |credit| credit.date <= target_aged_date }

      Money.sum(credits.map(&:credit))
    end

    def target_aged_date
      91.days.ago.to_date
    end
  end
end
