require 'rails_helper'

RSpec.describe 'reserving units from listings', :js do
  let!(:unit) { create(:unit, name: 'Unit 101') }

  let!(:listing) { create(:listing, :published, floorplan: unit.floorplan) }

  let!(:charge_preset) { create(:charge_preset, amount: '$100.00') }

  let!(:manager) { create(:property_manager, top_level: true) }

  before do
    # Designate property as preleasing
    unit.property.update!(preleasing: true)

    # Specify reservation charge preset
    unit.configuration.update!(reservation_charge_preset: charge_preset)

    # Enable notificaiton
    manager.notification_preferences.update!(
      notify_submitted_reservations: true
    )

    # Enable credit card payments for proeprty
    bank_account = create(:bank_account, owner: unit.property.company)
    create(:merchant_account,
           :zeamster,
           :credit_card,
           bank_account: bank_account)
  end

  scenario 'a user reserves a unit', vcr: {
    cassette_name: 'zeamster/application_checkout_credit_card' # Reuse tape
  } do
    visit listing_path(listing)

    click_on 'Reserve Now'

    open_new_tab

    row = find('tr', text: unit.name)

    within(row) { click_on 'Reserve' }

    open_new_tab

    # Contact Information
    fill_in 'First Name', with: '<PERSON>'
    fill_in 'Last Name', with: 'Ripley'
    fill_in 'Email', with: '<EMAIL>'
    fill_in 'Phone', with: '************'

    click_on 'Next'

    # Credit Card Information
    fill_in 'Card Number', with: '****************'
    fill_in 'Expires', with: '12 / 20'
    fill_in 'Security Code', with: '123'
    fill_in_address

    perform_enqueued_jobs do
      click_on 'Pay $102.89'

      chill { expect(page).to have_content(/thank you/i) }
    end

    # Tenant Profile
    tenant = Tenant.last!
    expect(tenant.name).to eq('Ellen Ripley')
    expect(tenant.email).to eq('<EMAIL>')
    expect(tenant.phone).to eq('+12484445550')

    # Reservation
    reservation = Unit::Reservation.last!
    expect(reservation.unit).to eq(unit)
    expect(reservation.tenant).to eq(tenant)

    # Invoice
    invoice = Invoice.last!
    expect(invoice.buyer).to eq(tenant)
    expect(invoice.seller).to eq(unit.property)
    expect(invoice.description).to eq("Reserve #{unit.name}")
    line_item = invoice.line_items.first!
    expect(line_item.description).to eq(charge_preset.name)
    expect(line_item.amount).to eq(charge_preset.amount)
    expect(line_item.receivable_account).to eq(charge_preset.account)
    expect(invoice).to be_paid

    # Payment
    payment = Payment.last!
    expect(payment.payer).to eq(tenant)
    expect(payment.payee).to eq(unit.property)
    expect(payment.amount).to eq(charge_preset.amount)

    # Confirmation Email
    email = ActionMailer::Base.deliveries.first
    expect(email.to).to eq([tenant.email])
    expect(email.subject).to eq("Reservation for #{unit.qualified_name}")

    # Notification
    notification = manager.notifications.last!
    expect(notification.title).to \
      eq("Reservation for #{unit.qualified_name} Submitted")
    expect(notification.description).to eq(tenant.name)
  end
end
