class CustomForms::UpdateSetting
  extend ::Service

  def initialize(form:, setting_params:)
    @form = form
    @setting_params = setting_params
  end

  def call
    setting.update! setting_params.permit(*automation_klass.permitted_keys)
    OpenStruct.new(successful?: true, setting: setting)
  rescue ActiveRecord::RecordInvalid => e
    OpenStruct.new(successful?: false, setting: setting, errors: e.record.errors.full_messages)
  end

  def automation_klass
    @automation_klass ||= CustomForms::AutomationNameToClass
                          .convert(setting_params[:automation_name])
  end

  def setting
    @setting ||= CustomForms::Automations.setting_by_name!(
      form_id: form.id,
      automation_name: setting_params[:automation_name]
    )
  end

  private

  attr_reader :form, :setting_params
end
