class Api::V2::DelinquencyResource < JSONAPI::Resource
  include Api::V2::Shared

  model_name 'Lease::Chain::AgingDelinquency::Materialized'

  attributes :balance,
             :overdue,
             :thirty_days,
             :sixty_days,
             :ninety_days,
             :ninety_one_plus_days

  def self.user_records(relation, user)
    leases = LeasesQuery.new.search.by_user(user).unarchived

    relation.where(lease_chain_id: leases.select(:id))
  end
end
