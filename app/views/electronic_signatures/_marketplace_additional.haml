- if @electronic_signature.needs_ach_information?
  .ui.very.padded.basic.vertical.segment
    .ui.very.basic.secondary.segment
      - if Customer.current_subdomain.start_with?('mph-sandbox', 'marketplacehomes')
        %p
          Marketplace Homes pulls rent on the
          %span
            1
            %sup> st
          of each month. To proceed with
          your lease agreement, you are required to place a bank account on file.
          This bank account should be where you want your rent to debit from each
          month.

    .two.required.fields
      .field
        = f.label :account_number
        = f.number_field :account_number

      .field
        = f.label :routing_number
        = f.number_field :routing_number
