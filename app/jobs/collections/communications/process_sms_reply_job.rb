class Collections::Communications::ProcessSmsReplyJob < ApplicationJob
  queue_as :within_one_day

  STOP_WORDS = %w[stop cancel end quit unsubscribe stopall].freeze

  def perform(payload:)
    @payload = payload

    if opt_out?
      Collections::OptOut.sms.create!(tenant: find_tenant!)
    else
      Rails.logger.info 'Collections reply did not include an opt out keyword.'
    end
  end

  private

  attr_reader :payload

  def opt_out?
    single_word_response = reply_body.downcase.squish

    STOP_WORDS.any? { |stop_word| single_word_response.eql?(stop_word) }
  end

  def find_tenant!
    Tenant.find_by!(phone: from_phone_number)
  rescue ActiveRecord::RecordNotFound
    raise "Unable to find tenant with phone '#{from_phone_number}' for opt out"
  end

  def reply_body
    payload['Body']
  end

  def from_phone_number
    payload['From']
  end
end
