require 'rails_helper'

RSpec.describe Telephony::PhoneNumbersController,
               devise_login: :property_manager do
  before do
    Customer.current.update!(
      twilio_account_sid: ENV.fetch('TWILIO_SID'),
      twilio_auth_token: ENV.fetch('TWILIO_AUTH_TOKEN')
    )
  end

  describe 'GET #index' do
    it 'returns http success' do
      get :index
      expect(response).to have_http_status(:success)
    end
  end

  describe 'GET #new' do
    it 'returns http success' do
      get :new
      expect(response).to have_http_status(:success)
    end
  end

  describe 'POST #search' do
    it 'renders the results template', vcr: {
      cassette_name: 'twilio/search_phone_numbers_successfully',
      record: :new_episodes
    } do
      post :search, params: { area_code: '313' }, format: :js
      expect(response).to render_template(:search)
    end
  end

  describe 'POST #create' do
    it 'redirects to the index', vcr: {
      cassette_name: 'twilio/search_phone_numbers_successfully',
      record: :new_episodes
    } do
      post :create, params: { phone_number: '+***********' }
      expect(response).to redirect_to(telephony_phone_numbers_path)
    end
  end

  describe 'DELETE #destroy' do
    it 'redirects to the index', vcr: {
      cassette_name: 'twilio/remove_phone_number_successfully'
    } do
      phone_number = create(:telephony_phone_number)

      expect do
        delete :destroy, params: { id: phone_number.id }
        expect(response).to redirect_to(telephony_phone_numbers_path)
      end.to change { Telephony::PhoneNumber.count }.by(-1)
    end
  end
end
