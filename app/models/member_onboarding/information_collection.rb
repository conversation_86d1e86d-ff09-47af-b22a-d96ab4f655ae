class MemberOnboarding::InformationCollection < ApplicationRecord
  ADDITIONAL_QUESTION_FIELDS = %w[type name options required].freeze
  include MemberOnboarding::WizardSerializable

  belongs_to :configuration,
             optional: false,
             class_name: 'MemberOnboarding::Configuration',
             inverse_of: :information_collection

  validates :nickname, :address, :student_id, :school_year,
            :collections_information, :drivers_license_number,
            inclusion: { in: [true, false] }, allow_nil: false

  validate :validate_additional_questions

  def additional_questions=(value)
    cleansed =
      value.compact_blank.map do |question|
        question['required'] = ActiveModel::Type::Boolean.new.cast(question['required'])
        question
      end
    super(cleansed)
  end

  private

  def validate_additional_questions
    (additional_questions || []).each do |question|
      validate_question_format(question)
      validate_question_type(question)
      validate_name(question)
      validate_dropdown(question)
    end
  end

  def validate_question_format(question)
    valid_question_format = question.is_a?(Hash) &&
                            question.keys.to_set(&:to_s) <= ADDITIONAL_QUESTION_FIELDS.to_set
    errors.add(:base, 'Invalid additional questions format') unless valid_question_format
  end

  def validate_question_type(question)
    question_types = %w[text dropdown description]
    valid_question_type = question_types.include?(question['type'])
    return if valid_question_type

    errors.add(:base, "#{name} must be a #{question_types.join(', or a')}")
  end

  def validate_name(question)
    return unless %w[text dropdown].include?(question['type'])

    errors.add(:base, 'Each Additional Question must have a name') if question['name'].blank?
  end

  def validate_dropdown(question)
    return unless question['type'] == 'dropdown'

    options = question['options'] || []
    errors.add(:base, 'Dropdowns require two options') if options.count < 2
    errors.add(:base, 'Each Dropdown option needs a value') if options.any?(&:blank?)
  end
end
