FactoryBot.define do
  factory :taxes_nelco_submission, class: 'Taxes::Nelco::Submission' do
    unsubmitted
    payments_total { '$20.00' }
    adjustment { '$0.00' }

    filing_period { Taxes.default_year }

    trait :for_owner do
      irs_filing factory: %i[taxes_irs_filing for_owner]
    end

    trait :for_vendor do
      irs_filing factory: %i[taxes_irs_filing for_vendor]
    end

    trait :with_batch do
      batch factory: %i[taxes_nelco_batch eula_consented]
    end
  end
end
