require 'swagger_helper'

require_relative 'rswag_context'

RSpec.describe 'api/v2/addresses' do
  include_context 'rswag api v2'

  let!(:address) do
    property = create(:property)

    api_user.property_memberships.create!(target: property)

    create(:address, addressable: property)
  end

  path '/api/v2/addresses' do
    get('list addresses') do
      response(200, 'successful') do
        run_test!
      end
    end
  end

  path '/api/v2/addresses/{id}' do
    parameter name: :id, in: :path, type: :integer, description: 'id'

    get('show address') do
      response(200, 'successful') do
        let(:id) { address.id }

        run_test!
      end
    end
  end
end
