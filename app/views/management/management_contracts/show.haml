= ActionsMenu::ManagementContract.(@management_contract)
= print_button

.ui.breadcrumb
  = link_to 'Entities', manage_companies_path, class: 'section'
  %i.right.angle.icon.divider

  = link_to @company.name, manage_company_path(@company), class: 'section'
  %i.right.angle.icon.divider

  %h1.ui.header
    Management Contract

.printable.paper
  %h1.ui.header
    Management Contract Summary
    .sub.header
      = @management_contract.company.name

  = render partial: 'settings_table',
    locals: { management_contract: @management_contract }

  = render partial: 'properties_table',
    locals: { management_contract: @management_contract }

  = render partial: 'account_settings_table',
    locals: { management_contract: @management_contract }

  = render partial: 'signature_history',
    locals: { management_contract: @management_contract }

= render ActionSidebar::ElectronicSignable::CountersignerModalComponent.new(electronic_signable: @management_contract)
