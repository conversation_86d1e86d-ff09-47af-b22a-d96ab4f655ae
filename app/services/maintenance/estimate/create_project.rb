class Maintenance::Estimate::CreateProject
  extend Service

  attr_reader :estimate, :user

  def initialize(estimate:, user:)
    @estimate = estimate
    @user = user
  end

  def call
    project = estimate.build_project(
      board: project_board,
      column: project_board_column,
      name: name.truncate(48),
      description: description,
      members: [user]
    )

    if expense_account
      project.budget_amounts.build(
        account: expense_account,
        amount: estimate.amount
      )
    end

    build_assessment_phase(project)

    if estimate.inspection
      # TODO: deprecated
      build_phase(project, 'Exterior', questions.where(category: :property))
      build_phase(project, 'Interior', questions.where.not(category: :property))
    else
      estimate.sections.each do |section|
        build_phase_from_section(project, section)
      end
    end

    ActiveRecord::Base.transaction do
      build_bill(project)
      project.save!
    end

    OpenStruct.new(successful?: true, project: project)
  end

  private

  def description
    'Rehab Project'
  end

  def build_assessment_phase(project)
    assessment_phase = project.phases.build(name: 'Assessment')

    if inspection
      assessment_phase.tasks.build(
        project: project,
        name: 'Inspection',
        kind: :inspection,
        inspection: inspection,
        completed_at: inspection.completed_at
      )
    end

    assessment_phase.tasks.build(
      project: project,
      name: 'Estimate',
      kind: :estimate,
      completed_at: estimate.created_at
    )

    assessment_phase.tasks.build(
      project: project,
      name: 'Funds Received',
      description: 'Mark as complete when funds are received'
    )
  end

  # Creates a phase from estimate tasks,
  #   question => card,
  #     estimate tasks => todolist item
  #
  # TODO: room_index
  def build_phase(project, name, questions)
    phase = project.phases.build(name: name)
    estimate_tasks = estimate.tasks.where(question: questions)
    estimate_tasks.group_by(&:question).each do |question, tasks|
      task = phase.tasks.build(
        project: project,
        name: question.prompt,
        kind: :todo_list
      )

      list = task.build_todo_list

      tasks.each do |task|
        list.todos.build(body: task.body)
      end
    end
  end

  def build_phase_from_section(project, section)
    assessment = project.phases.first

    phase = project.phases.build(name: section.name)

    section.areas.each do |area|
      card = phase.tasks.build(
        project: project,
        name: area.name,
        kind: :todo_list,
        phase_prerequisites: [assessment]
      )

      list = card.build_todo_list

      area.tasks.each do |task|
        list.todos.build(body: task.body)
      end
    end
  end

  def buyer
    if service_area.is_a?(Property)
      service_area
    else
      service_area.property
    end
  end

  def seller
    Customer.current.client_entity
  end

  def build_bill(project)
    return if estimate.amount.zero?

    date = Time.zone.today
    due_date = date + 30.days

    invoice = Invoice.create!(
      buyer: buyer,
      seller: seller,
      post_date: date,
      due_date: due_date,
      description: project.name
    ) do |invoice|
      invoice.line_items.build(
        receivable_account: seller.chart_of_accounts.default_maintenance_revenue_account,
        payable_account: expense_account,
        quantity: 1,
        description: project.name,
        unit_price: estimate.amount
      )
    end

    project.billings.build(
      estimate: estimate,
      invoice: invoice
    )
  end

  def expense_account
    service_area.configuration.chart_of_accounts.default_maintenance_expense_account
  end

  # TODO: choose board
  def project_board
    Project::Board.order(created_at: :desc).first
  end

  # TODO
  def project_board_column
    project_board.columns.first
  end

  delegate :inspection, :service_area, :template, to: :estimate
  delegate :questions, to: :template
  delegate :name, to: :service_area
end
