class ManagementFees::BillEntity
  extend Service

  attr_reader :entity, :effective_date, :mark_paid

  def initialize(entity:, effective_date:, mark_paid:)
    @entity = entity
    @effective_date = effective_date
    @mark_paid = mark_paid
  end

  def call
    invoices = []

    result = nil

    ActiveRecord::Base.transaction do
      properties.each do |property|
        result = ManagementFees::BillProperty.call(
          property: property,
          effective_date: effective_date,
          mark_paid: mark_paid
        )

        fail ActiveRecord::Rollback unless result.successful?

        invoices << result.invoice
      end

      entity.update!(management_fees_billed_through: effective_date)
    end

    OpenStruct.new(successful?: true, invoices: invoices.compact)
  rescue ActiveRecord::Rollback
    result
  end

  private

  def properties
    entity.properties.unarchived(effective_date.beginning_of_month)
  end
end
