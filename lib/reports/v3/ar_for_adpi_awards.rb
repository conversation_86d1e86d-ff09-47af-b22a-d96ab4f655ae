class Reports::V3::ArForAdpiAwards < Reports::V3::Report
  def name
    'AR for ADPi Awards'
  end

  def self.available_filters
    %i[portfolio date_range entity]
  end

  def self.default_filter_values
    # Handle fiscal year rollover
    start_date = if Time.zone.now > Date.new(Time.zone.today.year, 7, 1)
                   Date.new(Time.zone.today.year, 7, 1)
                 else
                   Date.new(Time.zone.today.year - 1, 7, 1)
                 end
    {
      start_date: start_date,
      end_date: Time.zone.today
    }
  end

  def mapping
    {
      entity: 'Chapter',
      total_billed: 'Total Billed',
      total_outstanding: 'Total Outstanding',
      percentage_outstanding: 'Percentage Outstanding'
    }
  end

  def basis
    Reports::V3::Basis::ArForAdpiAwards
  end

  def scope
    Reports::V3::Scope.new(user: user, filters: filters)
  end
end
