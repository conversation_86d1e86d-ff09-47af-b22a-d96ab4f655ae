require 'rails_helper'

RSpec.describe Importers::V3::Base do
  subject(:result) { importer_klass.call(file: file) }

  let(:importer_klass) { Importers::V3::GuestCards }

  let(:fixture_path) { 'spec/fixtures/importing/guest_cards.xlsx' }

  let(:file) { File.new(fixture_path) }

  before do
    create(:property_manager, first_name: 'Sam<PERSON>', last_name: 'Agent')
    unit = create(:unit)
    unit.floorplan.update!(name: '1BR-C')
    unit.property.update!(name: 'Sample Property')
    unit.portfolio.update!(name: 'Sample Portfolio')
  end

  describe 'handling invalid files' do
    context 'when the file is not an XLSX file' do
      let(:file) { File.new('spec/fixtures/note.txt') }

      it 'raises an error' do
        expect(result.errors).to match([/please ensure it is a valid xlsx file/i])
      end
    end

    context 'when the file is too large' do
      let(:maximum_file_size) { described_class::DEFAULT_MAXIMUM_FILE_SIZE }

      before do
        allow(file).to receive(:size) { maximum_file_size + 1 }
      end

      it 'raises an error' do
        expect(result.errors).to match([/file size exceeds the limit of 1 MB/i])
      end
    end

    context 'when the file is missing required columns' do
      let(:fixture_path) { 'spec/fixtures/importing/unit_directory.xlsx' }

      it 'raises an error' do
        expect(result.errors).to match(
          [/unable to find required columns 'date', 'first name', and 'last name'/i]
        )
      end
    end

    context 'when the file fails schema validation' do
      let(:fixture_path) { 'spec/fixtures/importing/guest_card_errors.xlsx' }

      it 'raises an error' do
        expected_errors = [
          'Date must be a date: Row 3',
          'Email is in invalid format: Row 3',
          'First Name must be filled (2): Rows 2 and 3',
          'Phone must be a valid phone number: Row 2',
          'Preferred Contact Method must be one of: email, phone, text: Row 3',
          'Preferred Style must be one of: studio, one bedroom, two bedroom, three bedroom, four bedroom: Row 3'
        ]

        expect(result.errors).to eq(expected_errors)
      end
    end
  end

  describe 'limiting import row count' do
    let(:maximum_row_count) { described_class::DEFAULT_MAXIMUM_ROW_LIMIT }

    before do
      allow_any_instance_of(Importers::V3::XLSXParser).to receive(:row_count) { provided_row_count }
    end

    context 'with less than or equal to the maximum number of rows' do
      let(:provided_row_count) { maximum_row_count }

      it { is_expected.to be_successful }
    end

    context 'with more than the maximum number of rows' do
      let(:provided_row_count) { maximum_row_count + 1 }

      it 'raises an error' do
        expect(result.errors).to match(
          [/row count of #{provided_row_count} exceeds the maximum limit of #{maximum_row_count}/i]
        )
      end
    end
  end
end
