class CreateZillowClaims < ActiveRecord::Migration[7.0]
  def change
    create_table :zillow_claims do |t|
      t.string :hotpads_unit_number, null: true
      t.string :zrm_property_id, null: true
      t.integer :hotpads_property_type, null: false
      t.boolean :show_address, null: false, default: true
      t.belongs_to :agent, foreign_key: { to_table: :property_managers }, null: true

      t.timestamps
    end
  end
end
