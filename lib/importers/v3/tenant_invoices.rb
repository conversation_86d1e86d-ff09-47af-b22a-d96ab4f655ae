class Importers::V3::TenantInvoices < Importers::V3::Base
  schema do
    column :property do
      required
      identifiers 'Property', 'Property Name'
      description 'The name of the property of the leased unit'
      sample '1234 Main Street'
    end

    column :unit do
      identifiers 'Unit', 'Unit Name'
      description 'The name of the leased unit'
      sample 'Unit 1'
    end

    column :first_name do
      required
      identifiers 'First Name', 'Tenant First Name'
      description 'The first name of the tenant'
      sample 'John'
    end

    column :last_name do
      required
      identifiers 'Last Name', 'Tenant Last Name'
      description 'The last name of the tenant'
      sample 'Smith'
    end

    column :email do
      identifiers 'Email'
      description 'The email of the tenant'
      sample '<EMAIL>'
    end

    column :date do
      required
      identifiers 'Date', 'Post Date'
      description 'The post date of the invoice'
      sample '1/1/2020'
    end

    column :due_date do
      required
      identifiers 'Due Date'
      description 'The due date of the invoice'
      sample '1/1/2020'
    end

    column :description do
      required
      identifiers 'Description', 'Invoice Description'
      description 'The description of the invoice'
      sample 'February Rent'
    end

    column :invoice_number do
      identifiers 'Invoice Number'
      description 'The invoice number'
      sample '1001'
    end

    column :notification_preference do
      identifiers 'Notification Preference'
      description 'Preference for sending a notification for this invoice'
      sample 'Never'
      enum Invoice.notification_preferences
    end

    column :line_item do
      identifiers 'Line Item', 'Line Item Description'
      description 'The description of the line item'
      sample 'Monthly Rent'
    end

    column :account do
      required
      identifiers 'Account', 'GL Code'
      description 'The GL code of the receivable account'
      sample '4100 - Rent'
    end

    column :amount do
      required
      identifiers 'Amount'
      description 'The amount of the line item'
      sample '$100.00'
    end

    column :notes do
      identifiers 'Note', 'Notes'
      description 'Extra notes'
    end
  end

  protected

  def process_item(items)
    item = items.first

    property = property(item)
    unit = unit(item, property)

    lease_membership = lease_membership(item, property, unit)

    tenant = if lease_membership
               lease_membership.tenant
             else
               simple_agreement_membership(item, property)&.tenant
             end

    unless tenant
      first_name = item.value(identifiers(:first_name))
      last_name = item.value(identifiers(:last_name))

      fail Importers::V3::Errors::UnrecoverableError,
           "Unable to find tenant '#{first_name} #{last_name}' at property '#{property.name}'"
    end

    description = item.value(identifiers(:description))

    attributes = {
      buyer: tenant,
      buyer_lease_membership: lease_membership,
      seller: property,
      post_date: item.value(identifiers(:date)),
      physical_date: item.value(identifiers(:date)),
      due_date: item.value(identifiers(:due_date)),
      description: description,
      invoice_number: item.value(identifiers(:invoice_number)),
      note: item.value(identifiers(:notes)),
      notification_preference: notification_preference(item),
      line_items_attributes: items.map do |item|
        {
          quantity: 1,
          unit_price: item.value(identifiers(:amount)),
          description: item.value(identifiers(:line_item)) || description,
          receivable_account: account(item, property)
        }
      end
    }

    invoice = Invoice.create!(attributes)

    record_imported(invoice)
  end

  private

  def property(item)
    name = item.value(identifiers(:property))

    Property.unarchived.find_by!(name: name)
  rescue ActiveRecord::RecordNotFound
    raise Importers::V3::Errors::UnrecoverableError,
          "Unable to find property '#{name}'"
  end

  def unit(item, property)
    name = item.value(identifiers(:unit))

    return if name.blank?

    property.units.unarchived.find_by!(name:)
  rescue ActiveRecord::RecordNotFound
    raise Importers::V3::Errors::UnrecoverableError,
          "Unable to find unit '#{name}' at property '#{property.name}'"
  end

  def lease_membership(item, property, unit)
    query = LeaseMembership.unarchived

    query = if unit
              query.joins(:tenant, :lease).merge(Lease.where(unit:))
            else
              query.joins(:tenant, lease: :unit).merge(Unit.where(property:))
            end

    query.merge(find_tenant_query(item)).last
  end

  def simple_agreement_membership(item, property)
    Agreements::SimpleAgreement::Membership
      .joins(:simple_agreement, :tenant)
      .merge(Agreements::SimpleAgreement.where(property: property))
      .merge(find_tenant_query(item))
      .last
  end

  def notification_preference(item)
    value = item.value(identifiers(:notification_preference))

    return 'on_create' if value.blank?

    value.downcase.parameterize.underscore
  end

  def account(item, property)
    value = item.value(identifiers(:account)).to_s

    return nil if value.blank?

    gl_code = value.split(/[^\d]/).first

    chart_of_accounts = property.company.chart_of_accounts

    chart_of_accounts.accounts.find_by!(gl_code: gl_code)
  rescue ActiveRecord::RecordNotFound
    raise Importers::V3::Errors::UnrecoverableError,
          "Unable to find account with GL code '#{gl_code}' " \
          "in chart of accounts '#{chart_of_accounts.name}'"
  end

  def find_tenant_query(item)
    first_name = item.value(identifiers(:first_name))
    last_name = item.value(identifiers(:last_name))
    email = item.value(identifiers(:email))

    query = Tenant.where(first_name: first_name, last_name: last_name)

    query = query.where(email:) if email.present?

    query
  end

  def max_row_limit
    CustomerSpecific::Behavior.importers_tenant_invoices_max_row_limit || 1000
  end

  def processable_items(&)
    items = parser.each_item.to_a.map(&:first)

    group_keys = %i[
      property unit first_name last_name email
      date due_date invoice_number description notification_preference
    ]

    groups = items.group_by do |item|
      group_keys.map do |key|
        item.value(identifiers(key))
      end
    end

    total = groups.size

    groups.values.each.with_index do |group, index|
      yield group, index, total
    end
  end
end
