class Taxes::Nelco::Transmit::Preparers::PayerCompany
  extend ActiveModel::Naming
  include ActiveModel::Validations

  attr_reader :payer

  REQUIRED_FIELDS = %i[taxpayer_identification address representative
                       email name].freeze
  REQUIRED_FIELDS.each do |field_name|
    validates field_name, presence: true
  end

  def preparers
    @preparers ||= Taxes::Nelco::Transmit::Preparers
  end

  def initialize(payer)
    @errors = ActiveModel::Errors.new(self)
    @payer = payer
  end

  def render(document)
    Nokogiri::XML::Builder.with(document) do |xml|
      xml.PayerCompany do
        xml.Name1 formatted_name
        xml.InCareOfName "% #{contact_name}" unless pats?
        preparers::Address.new(address).render(xml.parent)
        xml.EmailAddress email unless pats?
        preparers::Phone.new(phone).render(xml.parent) if phone
        xml.ContactName contact_name[...27] unless pats?
      end
    end
  end

  def formatted_name
    name.strip.gsub(/\s\s+/, ' ')
        .gsub('&', 'and')
        .gsub(/[^A-Za-z0-9#.\-() ]/, '')[...75]
  end

  def contact_name
    "#{representative.first_name} #{representative.last_name}"
      .gsub('&', 'and')
      .gsub(/\s\s+/, ' ').gsub(
        /[^\-a-zA-Z0-9 ]/, ''
      )[...32]
  end

  def email
    contact_email.presence || representative&.email.presence
  end

  def pats?
    Taxes::Nelco.pats?
  end

  delegate :name, :contact_email, :phone, :formatted_phone,
           :representative, :address, :taxpayer_identification, to: :payer
end
