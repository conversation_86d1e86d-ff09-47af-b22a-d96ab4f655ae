require 'rails_helper'

RSpec.describe LeaseApplicationsQuery do
  describe '#in_progress' do
    subject { described_class.new.search.in_progress }

    context 'a lease application that was just started' do
      let!(:application) { create(:lease_application, :invite) }

      it { is_expected.to include(application) }
    end

    context 'a completed lease application' do
      let!(:application) { create(:lease_application, :completed) }

      it { is_expected.to include(application) }
    end

    context 'an expired application' do
      let!(:application) do
        create(:lease_application, :completed, expires_at: 5.minutes.ago)
      end

      it { is_expected.not_to include(application) }
    end

    context 'a submitted lease application' do
      let!(:application) { create(:lease_application, :submitted) }

      it { is_expected.not_to include(application) }
    end
  end

  describe '#pending_review' do
    subject { described_class.new.search.pending_review }

    context 'with a unsubmitted application' do
      let!(:application) { create(:lease_application, :completed) }

      it { is_expected.not_to include(application) }
    end

    context 'with a submitted application' do
      let!(:application) { create(:lease_application, :submitted) }

      it { is_expected.not_to include(application) }
    end

    context 'with a paid application' do
      let!(:application) { create(:lease_application, :screening_initiated) }

      it { is_expected.to include(application) }
    end

    context 'with a rejected application' do
      let!(:application) { create(:lease_application, :rejected) }

      it { is_expected.not_to include(application) }
    end

    context 'with an approved application' do
      let!(:application) { create(:lease_application, :approved) }

      it { is_expected.not_to include(application) }
    end
  end
end
