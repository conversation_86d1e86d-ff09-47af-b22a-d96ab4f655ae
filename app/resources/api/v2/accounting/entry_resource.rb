class Api::V2::Accounting::EntryResource < JSONAPI::Resource
  include Api::V2::Shared

  model_name 'Plutus::Entry'

  attributes :date, :description
  attribute :reference, delegate: :reference_text
  attributes :document_type, :closing_entry
  attributes :created_at, :updated_at

  has_many :amounts, exclude_links: :default
  has_one :property, exclude_links: :default
  has_one :lease, exclude_links: :default

  exclude_links :default

  filter :start_date,
         verify: verify_date_filter,
         apply: lambda { |records, value, options|
           if value
             records.where(date: value..)
           else
             records
           end
         }

  filter :end_date,
         verify: verify_date_filter,
         apply: lambda { |records, value, options|
           if value
             records.where(date: ..value)
           else
             records
           end
         }

  def document_type
    case @model.commercial_document_type
    when 'Invoice' then 'invoice'
    when 'Payment' then 'payment'
    else 'journal_entry'
    end
  end

  def closing_entry
    @model.retained_earnings?
  end

  def self.records(options = {})
    # The amounts endpoint already checks for an appropriate journal, basis, and
    # lock date, so we don't need to filter to ensure that we are fetching an
    # accessible entry.
    return super(options) if we_are_being_included_from_the_amounts_endpoint?(options)

    journal = options[:context][:journal]
    basis = options[:context][:basis]

    filter = if basis == 'cash'
               journal.journal_entries.not_accrual_basis
             else
               journal.journal_entries.not_cash_basis
             end

    policy = Api::V2::Accounting::UnlockedPeriodsPolicy.new(
      api_key: options[:context][:api_key]
    )

    unless policy.allowed_to?(:access_unlocked_periods?)
      filter = if journal.locked_at
                 filter.where(date: ..journal.locked_at)
               else
                 filter.none
               end
    end

    super(options).merge(filter)
  end
end
