import { toFinite } from 'lodash';
import { Controller } from 'stimulus';
import { formatMoney } from '../../../../util/money';
import { renderUjsErrors } from '../../../../util/render_ujs_errors';

export default class extends Controller {
  static targets = [
    'accountDisplay',
    'accountSelector',
    'contextMessage',
    'createReceipt',
    'forwardInvoice',
    'forwardReceipt',
    'forwardSearch',
    'invoiceCopy',
    'invoiceDescription',
    'invoiceRows',
    'invoiceSelectorTemplate',
    'markup',
    'matchingInvoiceTable',
    'propertySelector',
    'recoupAmount',
    'totalAmount',
    'totalRecoupAmount',
    'transactionAmount',
    'vendorSelector',
    'transactionDescription'
  ];

  static values = {
    matchingInvoiceUrl: String,
    transactionContext: String,
    canPreferNewVendor: Array
  };

  async connect() {
    this.accountValues = null;
    this.forwardToName = this.forwardInvoiceTarget.dataset.forwardToName;
    this.propertyId = this.hasPropertySelectorTarget ? $(this.propertySelectorTarget).val() : undefined;
    this.selectedInvoiceId = this.matchingInvoiceTableTarget.dataset.selectedInvoiceId;
    this.transactionAmount = parseInt(this.transactionAmountTarget.dataset.invoiceAmount);
    this.vendorDropdown = $(this.vendorSelectorTarget).dropdown();
    this.vendorId = $(this.vendorSelectorTarget).val();

    this.updateContextMessage();

    if (this.accountSelectorTarget.dataset.accountId) {
      this.updateAccount();
    }

    this.updateDescription();
    this.prepareForwardingSearch();
    this.prepareFormValidation();
    this.prepareMarkupInputs();

    this.computeTotals();

    await this.findMatchingInvoices(this.selectedInvoiceId);
  }

  addVendorError = (ajaxError) => {
    ajaxError.preventDefault();
    const vendorJson = JSON.parse(ajaxError.detail[2].responseText);
    renderUjsErrors('#vendor_form', vendorJson.errors);
  }

  addVendor = (ajaxSuccess) => {
    ajaxSuccess.preventDefault();
    $('#vendor_form').form('reset');
    const vendorJson = JSON.parse(ajaxSuccess.detail[2].responseText);

    const existing_options = $(this.vendorSelectorTarget).find('option')
      .map((option) => {
        return { text: option.text, name: option.text, value: option.value }
      })
    const new_option =
      { text: vendorJson.name, name: vendorJson.name, value: vendorJson.id.toString() };
    this.vendorDropdown.dropdown('change values', existing_options.toArray().concat(new_option))
    this.vendorDropdown.dropdown('set selected', vendorJson.id.toString());
    return ajaxSuccess;
  }

  buildSelectInvoiceTable = (invoicesHtml, selectedInvoiceId) => {
    this.clearInvoiceRows();

    const invoiceCount = (invoicesHtml.match(/invoice-row/g) || []).length;

    if (invoiceCount == 0) {
      this.invoiceCopyTarget.style.display = 'none';
    } else {
      const matchingInvoiceString = 'INVOICE_NUMBER matching invoicePLURAL found. Select an invoice to link or create a new invoice.'
        .replace(/INVOICE_NUMBER/g, invoiceCount)
        .replace(/PLURAL/g, invoiceCount > 1 ? 's' : '');

      this.invoiceCopyTarget.innerText = matchingInvoiceString;
      this.invoiceCopyTarget.style.display = 'block';
    }

    this.invoiceRowsTarget.insertAdjacentHTML('afterbegin', invoicesHtml);

    if (selectedInvoiceId) {
      const $expectedRadio = $(this.invoiceRowsTarget).find(`:radio[value=${selectedInvoiceId}]`);

      if ($expectedRadio.length) {
        $expectedRadio[0].checked = true;
      } else {
        $(this.invoiceRowsTarget).find(':radio[value=""]')[0].checked = true;
      }
    } else {
      $(this.invoiceRowsTarget).find(':radio[value=""]')[0].checked = true;
    }

    if (invoiceCount == 0) {
      $('#invoices_found').hide();
      $('#no_invoices_found').show();
      $('#loading_invoices').hide();
    } else {
      $('#invoices_found').show();
      $('#no_invoices_found').hide();
      $('#loading_invoices').hide();
    }
  };

  clearInvoiceRows = () => {
    var invoiceRow = document.getElementsByClassName('invoice-row');

    while (invoiceRow[0]) {
      invoiceRow[0].parentNode.removeChild(invoiceRow[0]);
    }
  };

  computeTotals = () => {
    const markupInput = $(this.markupTarget).find('input.markup.input');
    const markupKindInput = $(this.markupTarget).find('input.markup-kind.input');
    const markupValue = toFinite(markupInput.val()) || 0;
    let markupCents = 0;

    if (markupKindInput.val() === 'fixed' && markupValue) {
      markupCents = markupValue * 100;
    } else if (markupValue || markupInput.val() == '0') {
      markupCents = (this.transactionAmount * (markupValue / 100));
    } else {
      markupCents = this.transactionAmount * 0; // 0% default markup
    }

    const transactionTotal = formatMoney(this.transactionAmount, true);
    this.transactionAmountTarget.innerText = transactionTotal;
    this.totalAmountTarget.innerText = transactionTotal;

    this.recoupAmountTarget.innerText = formatMoney(markupCents, true);
    this.totalRecoupAmountTarget.innerText = formatMoney(this.transactionAmount + markupCents, true);
  };

  findMatchingInvoices = async (selectedInvoiceId) => {
    $('#invoices_found').hide();
    $('#no_invoices_found').hide();
    $('#loading_invoices').show();

    let url = this.matchingInvoiceUrlValue + '?';

    if (this.vendorId) {
      url += `vendor_id=${this.vendorId}&`;
    }

    if (this.propertyId) {
      url += `property_id=${this.propertyId}&`;
    }

    const response = await fetch(url);
    const invoicesHtml = await response.text();
    this.buildSelectInvoiceTable(invoicesHtml, selectedInvoiceId);
  };

  initVendorForm = (e) => {
    if ($('#vendor_scoped_to_gids').length === 0) {
      return e;
    }

    const propertyIds = this.propertyId?.split(',') ?? [];
    const lockedScopedToGIDs = propertyIds.map((id) => {
      return `gid://revela/Property/${id}`;
    });

    const dropdownOptions = this.canPreferNewVendorValue.map((opt) => {
       if (lockedScopedToGIDs.includes(opt.value)) {
        return { ...opt, selected: true };
       } else {
         return opt;
       }
    });

    const initScopedToEvent = new CustomEvent('ScopedToDropdown:init', {
      detail: {
        dropdownOptions,
        lockedScopedToGIDs
      }
    });
    document.querySelector('#vendor_scoped_to_gids').dispatchEvent(initScopedToEvent);
    return e;
  }

  prepareAccountDropdown = () => {
    $(this.accountSearchTarget).dropdown({
      forceSelection: true,
      fullTextSearch: true,
      onChange(value) {
        $(this).find('#accounting_debit_card_purchase_account_id').val(value);
      },
    });
  };

  prepareCreateFormValidation = () => {
    $('form#transaction-form').form({
      fields: {
        'accounting_debit_card_purchase[description]': 'empty',
        'accounting_debit_card_purchase[vendor_id]': 'empty',
        'accounting_debit_card_purchase[account_id]': 'empty'
      },
    });
  };

  prepareForwardingSearch = () => {
    $(this.forwardSearchTarget).search({
      type: 'category',
      selectFirstResult: true,
      cache: false,
      apiSettings: {
        url: '/accounting/targets.json?q={query}',
      },
      onSelect(result) {
        $(this).find('#accounting_debit_card_purchase_forward_sgid').val(result.sgid);
      },
    });

    if (this.forwardToName)
      $(this.forwardSearchTarget).find('input.prompt').val(this.forwardToName);
  };

  prepareFormValidation = () => {
    if (this.transactionContextValue == 'forward') {
      this.prepareForwardFormValidation();
    } else {
      this.prepareCreateFormValidation();
    }
  };

  prepareForwardFormValidation = () => {
    $('form#transaction-form').form({
      fields: {
        'accounting_debit_card_purchase[description]': 'empty',
        'accounting_debit_card_purchase[vendor_id]': 'empty',
        'accounting_debit_card_purchase[account_id]': 'empty',
        'accounting_debit_card_purchase[forward_sgid]': 'empty'
      },
    });
  };

  prepareMarkupInputs = () => {
    $(this.markupTarget).find('input.money.input').on('blur', this.computeTotals);
    $(this.markupTarget).find('input.quantity.input').on('change', this.computeTotals);
    $(this.markupTarget).find('input.markup.input').on('change', this.computeTotals);
    $(this.markupTarget).find('input.markup-kind.input').on('change', this.computeTotals);
  };

  toggleCreateReceipt = () => {
    this.transactionContextValue = 'create';
    this.updateContextMessage();
    this.createReceiptTarget.classList.add('active');
    this.forwardReceiptTarget.classList.remove('active');

    if (this.hasPropertySelectorTarget) {
      $(this.propertySelectorTarget).closest('.field').show();
    }

    $(this.forwardInvoiceTarget).closest('.field').hide();
    $(this.forwardSearchTarget).find('input.prompt').val("");
    $('#accounting_debit_card_purchase_forward_sgid').val("");
    $('#accounting_debit_card_purchase_markup_raw').val(0);

    this.prepareCreateFormValidation();
  }

  toggleForwardReceipt = () => {
    this.transactionContextValue = 'forward';
    this.updateContextMessage();
    this.forwardReceiptTarget.classList.add('active');
    this.createReceiptTarget.classList.remove('active');

    if (this.hasPropertySelectorTarget) {
      $(this.propertySelectorTarget).closest('.field').hide();
      $(this.propertySelectorTarget).val('').trigger('change');
    }

    this.computeTotals();
    $(this.forwardInvoiceTarget).closest('.field').show();

    this.prepareForwardFormValidation();
  }

  transactionContextValueChanged() {
    this.updateContextMessage();
  }

  updateAccount = () => {
    this.accountDisplayTarget.innerText = $('#accounting_debit_card_purchase_account_id :selected').text();
    this.accountDisplayTarget.style.color = 'rgba(0,0,0,1)';
  };

  updateContextMessage = () => {
    const message = this.transactionContextValue === 'forward'
      ? 'Forwarding a receipt will record both an invoice/payment from the vendor, as well as a forwarded invoice with a credit note.'
      : 'Creating a receipt records an invoice and payment from the selected vendor.';
    this.contextMessageTarget.querySelector('p').textContent = message;
  }

  updateDescription = () => {
    $(this.invoiceDescriptionTarget).text($(this.transactionDescriptionTarget).val());
  };

  updateProperty = ({ target }) => {
    this.propertyId = $(target).val();
    this.findMatchingInvoices();
  };

  updateVendor = ({ target }) => {
    this.vendorId = $(target).val();
    this.findMatchingInvoices();
  };
}
