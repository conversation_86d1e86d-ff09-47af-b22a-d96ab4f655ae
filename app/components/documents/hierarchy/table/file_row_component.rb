class Documents::Hierarchy::Table::FileRowComponent < ApplicationComponent
  def initialize(file:)
    super()
    @file = file
  end

  private

  attr_reader :file

  def download_path
    file.try(:expiring_url) || file.url
  end

  def file_name
    file.filename
  end

  def file_type
    file.decorate.display_content_type
  end

  def time_stamp
    file.created_at.to_s(:short_datetime)
  end

  def icon_class
    if image?
      'file image outline'
    else
      'file outline'
    end
  end

  def image?
    file_name.to_s.ends_with?('jpg', 'jpeg', 'png')
  end
end
