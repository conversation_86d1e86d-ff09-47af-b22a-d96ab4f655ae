require 'securerandom'

class Approvals::Request < ApplicationRecord
  belongs_to :requested_by, polymorphic: true
  belongs_to :approver, polymorphic: true
  belongs_to :approvable, polymorphic: true
  has_one :approval, dependent: :destroy
  has_one :rejection, dependent: :destroy

  validates :approvable, presence: true
  validates :action, presence: true
  validates :requested_by, presence: true
  validates :approver, presence: true

  has_secure_token

  enum :action, Approvals::Rule.actions

  scope :pending, lambda {
    where(id: where.missing(:approval)).where(id: where.missing(:rejection))
  }

  # TODO: Is this the same as pending? Rejected approvals?
  scope :unsatisfied,
        -> { left_joins(:approval).where(approvals_approvals: { id: nil }) }

  scope :satisfied,
        -> { joins(:approval) }

  def satisfied?
    satisfied_with.present?
  end

  def satisfied_with
    approval || rejection # TODO: any approval or rejection on approvable for action
  end
end
