require 'rails_helper'

RSpec.describe 'risk_release/enrollments' do
  subject(:result) do
    headers = {
      'Authorization' => "Bearer #{partner_token}",
      'Content-Type' => 'application/vnd.apijson'
    }

    endpoint = '/risk_release/enrollments.json'

    endpoint += "?page[number]=#{page_number}" if page_number.present?

    get endpoint, headers: headers

    response
  end

  before do
    host! 'public.lvh.me'

    # For Customer representative
    create(:property_manager)
  end

  let(:partner_token) { create(:partner_token, partner_name: :risk_release_enrollments).token }
  let(:page_number) { nil }

  describe 'without valid token' do
    let(:partner_token) { 'invalid' }

    it 'returns unauthorized' do
      expect(result).to have_http_status(:unauthorized)
    end
  end

  describe 'endpoint data' do
    subject(:data) do
      result.parsed_body['data'].first['attributes']
    end

    let!(:simple_agreement_membership) do
      create(:agreements_simple_agreement_membership)
    end

    before do
      allow(Flipper[:risk_release]).to receive(:actors_value).and_return(['alever'])
    end

    context 'when tenant has a lease membership' do
      it 'uses data from lease' do
        lease_membership = create(:lease_membership)
        create(:risk_release_enrollment,
               lease_membership: lease_membership,
               simple_agreement_membership: simple_agreement_membership)

        expect(result).to have_http_status(:ok)
        expect(data.dig('contact', 'id')).to eq("alever/tenants/#{lease_membership.tenant.id}")
        expect(data.dig('lease', 'id')).to eq("alever/leases/#{lease_membership.lease.id}")
        expect(data.dig('unit', 'id')).to eq("alever/units/#{lease_membership.unit.id}")
      end
    end

    context 'when tenant does not have lease membership' do
      it 'uses data from simple agreement' do
        create(:risk_release_enrollment,
               simple_agreement_membership: simple_agreement_membership)

        expect(result).to have_http_status(:ok)
        expect(data.dig('contact', 'id')).to \
          eq("alever/tenants/#{simple_agreement_membership.tenant.id}")
        expect(data.dig('lease', 'id')).to be_nil
        expect(data.dig('unit', 'id')).to be_nil
      end
    end
  end

  describe 'endpoint pagination links' do
    subject(:links) { result.parsed_body['links'] }

    context 'when the only customer' do
      it 'returns no previous nor next' do
        allow(Flipper[:risk_release]).to receive(:actors_value).and_return(['alever'])

        expect(links['previous']).to be_nil
        expect(links['next']).to be_nil
      end
    end

    context 'when first customer' do
      it 'returns no previous' do
        allow(Flipper[:risk_release]).to receive(:actors_value).and_return(%w[alever balever])

        expect(links['previous']).to be_nil
        expect(links['self']).to eq('http://public.lvh.me/risk_release/enrollments.json?page%5Bnumber%5D=1')
        expect(links['next']).to eq('http://public.lvh.me/risk_release/enrollments.json?page%5Bnumber%5D=2')
      end
    end

    context 'when there are customers before and after' do
      let(:page_number) { 2 }

      it 'returns both previous and next' do
        allow(Flipper[:risk_release]).to \
          receive(:actors_value).and_return(%w[aalever alever balever])

        expect(links['previous']).to eq('http://public.lvh.me/risk_release/enrollments.json?page%5Bnumber%5D=1')
        expect(links['self']).to eq('http://public.lvh.me/risk_release/enrollments.json?page%5Bnumber%5D=2')
        expect(links['next']).to eq('http://public.lvh.me/risk_release/enrollments.json?page%5Bnumber%5D=3')
      end
    end

    context 'when last customer' do
      let(:page_number) { 2 }

      it 'returns no next link' do
        allow(Flipper[:risk_release]).to receive(:actors_value).and_return(%w[aalever alever])

        expect(links['previous']).to eq('http://public.lvh.me/risk_release/enrollments.json?page%5Bnumber%5D=1')
        expect(links['self']).to eq('http://public.lvh.me/risk_release/enrollments.json?page%5Bnumber%5D=2')
        expect(links['next']).to be_nil
      end
    end
  end
end
