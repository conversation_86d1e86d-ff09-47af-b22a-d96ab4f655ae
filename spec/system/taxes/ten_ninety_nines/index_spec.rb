require 'rails_helper'

RSpec.describe '1099 index', :js, capybara_login: :property_manager do
  before do
    travel_to(Date.new(2000, 1, 1))

    create(:taxes_nelco_account) # Enable 1099s page

    date = Date.new(1999, 12, 31) # Add accounting activity in year 1999

    amount = Money.new(1_000_00) # Above $600.00

    company = Customer.current.client_entity # Owned entity

    [vendor_one, vendor_two].each do |vendor|
      create(:payment,
             payer: company,
             payee: vendor,
             date: date,
             amount: amount)
    end

    Taxes::Candidate1099.refresh!
  end

  let!(:vendor_one) { create(:vendor, name: 'Vendor One') }

  let!(:vendor_two) { create(:vendor, name: 'Vendor Two') }

  scenario 'user filters by search' do
    visit taxes_ten_ninety_nines_path

    expect(page).to have_content(vendor_one.name)
    expect(page).to have_content(vendor_two.name)

    fill_in 'Search...', with: vendor_two.name

    expect(page).to have_no_content(vendor_one.name)
    expect(page).to have_content(vendor_two.name)
  end
end
