class CreateLeaseApplicationResidences < ActiveRecord::Migration[6.1]
  def change
    create_table :lease_application_residences do |t|
      t.references :lease_application, foreign_key: true, null: false
      t.integer :applicant_id, null: false
      t.string :name, null: false
      t.monetize :monthly_amount, currency: { present: false }, amount: { null: true }
      t.string :reason_for_moving
      t.string :contact
      t.string :email
      t.string :phone
      t.date :start_date
      t.date :end_date

      t.timestamps
    end

    unless Customer.current_subdomain == 'demo'
      reversible do |direction|
        direction.up do
          LeaseApplication.find_each do |application|
            fields = application.fields

            residences_attributes = Array.wrap(
              fields['previous_residence'].presence ||
              fields['previous_residences'].presence
            )

            # Migrate legacy form attributes to persisted records
            residences_attributes.each do |attributes|
              monthly_amount = Monetize.parse(attributes['monthly_amount'])

              address = Address.new(attributes['address'])

              application.residences.create!(
                applicant_id: attributes['applicant_id'] || application.applicants.first.id,
                name: attributes['name'],
                monthly_amount: monthly_amount.format,
                start_date: attributes['start_date'],
                end_date: attributes['end_date'],
                reason_for_moving: attributes['reason_for_moving'],
                contact: attributes['contact'],
                email: attributes['email'],
                phone: attributes['phone'],
                address_attributes: (
                  if address.valid?
                    attributes['address']
                  else
                    {}
                  end
                )
              )
            end
          end
        end
      end
    end
  end
end
