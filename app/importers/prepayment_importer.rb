class PrepaymentImporter < Importers::Importer::V2
  include Importers::XLSX

  def initialize(path:)
    super(path: path)
  end

  def results_key
    :payments
  end

  def import_row(row)
    amount = Monetize.parse(row.value('Amount'))

    return unless amount.positive?

    membership = lease_membership(row)

    Payment.create!(
      date: row.value('Date'),
      description: row.value('Description'),
      amount: row.value('Amount'),
      check_number: row.value('Check Number'),
      payer: membership.tenant,
      payer_lease_membership: membership,
      payee: membership.property
    )
  end

  def lease_membership(row)
    property = Property.find_by!(name: row.value('Property'))

    unit = property.units.find_by!(name: row.value('Unit'))

    tenant_name = row.value('Tenant')

    first_name = nil
    last_name = nil

    if tenant_name.include?(',')
      last_name, first_name = tenant_name.split(',').map(&:strip)
    else
      first_name, last_name = tenant_name.reverse.split(' ', 2).map(&:reverse).reverse.map(&:strip)
    end

    # Find by last name to avoid middle initial
    LeaseMembership
      .order(id: :asc)
      .joins(:tenant, lease: :unit)
      .find_by!(
        leases: { unit: unit },
        tenants: { last_name: last_name }
      )
  end
end
