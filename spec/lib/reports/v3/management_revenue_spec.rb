require 'rails_helper'

require_relative 'shared'

require_relative 'shared/management_portfolio_activity_context'

RSpec.describe Reports::V3::ManagementRevenue do
  include_context 'with management portfolio activity'

  let(:report) { described_class.new(filters: filters, user: user) }

  let(:user) { create(:property_manager, top_level: true) }

  let(:filters) { OpenStruct.new(start_date: Time.zone.today, end_date: Time.zone.today) }

  context 'when unfiltered' do
    # Assumes cash basis.
    # First invoice is completely paid, markup paid to $50.00, contributes
    #   $100.00 HVAC Expense
    #   $50.00 HVAC Revenue
    # Second invoice is partially paid, contributes
    #   ratio = 100.00 / 221.05
    #
    #   $34.16 Plumbing Expense
    #   $75.50 Plumbing Revenue
    #   $56.70 Plumbing Expense
    #   $125.33 Plumbing Revenue
    #   $9.15 Office Expense
    # Third invoice is half paid, contributes
    #   $10.00 Office Expense
    #
    # Net
    # $50.00 HVAC Revenue
    # $200.83 Plumbing Revenue
    # / 250.83
    #
    # $100.00 HVAC Expense
    # $90.86 Plumbing Expense
    # $19.15 Office Expense
    # / 210.01
    # // 40.82

    it_behaves_like 'a v3 report'
  end

  context 'when filtered by portfolio one' do
    # Invoice one completely forwarded to property one, contributes:
    #   $100.00 HVAC Expense
    #   $50.00 HVAC Revenue
    # Invoice two partially forwarded to property one, contributes:
    #   $34.16 Plumbing Expense
    #   $75.50 Plumbing Revenue
    #
    # Net
    # $50.00 HVAC Revenue
    # $75.50 Plumbing Revenue
    # / 125.50
    #
    # $100.00 HVAC Expense
    # $34.16 Plumbing Expense
    # / 134.16
    # // -8.66

    let(:filters) do
      OpenStruct.new(
        start_date: Time.zone.today,
        end_date: Time.zone.today,
        customer_portfolio_id: portfolio_one.id.to_s # TODO: needs to be a string...
      )
    end

    it_behaves_like 'a v3 report'
  end

  context 'when filtered by portfolio two' do
    # Invoice two partially forwarded to property two, contributes:
    #   $56.70 * ratio Plumbing Expense
    #   $125.33 Plumbing Revenue
    #
    # Net
    # $125.33 Plumbing Revenue
    # / 125.33
    #
    # $56.70 Plumbing Expense
    # / 56.70
    # // 68.63

    let(:filters) do
      OpenStruct.new(
        start_date: Time.zone.today,
        end_date: Time.zone.today,
        customer_portfolio_id: portfolio_two.id.to_s # TODO: needs to be a string...
      )
    end

    it_behaves_like 'a v3 report'
  end

  context 'when filtered by portfolio none' do
    # Invoice two has an unforwarded item, partially paid, contributes
    #   $9.15 Office Expense
    # Invoice three is completely unforwarded, half paid, contributes
    #   $10.00 Office Expense
    #
    # // 19.15

    let(:filters) do
      OpenStruct.new(
        start_date: Time.zone.today,
        end_date: Time.zone.today,
        customer_portfolio_id: 'none'
      )
    end

    it_behaves_like 'a v3 report'
  end
end
