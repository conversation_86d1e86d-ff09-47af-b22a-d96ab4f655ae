require 'rails_helper'

RSpec.describe Inspections::Wizard do
  subject(:wizard) { described_class.new(inspection: inspection) }

  context 'with a multifamily property inspection' do
    # 1 Property
    # 2   Common Room
    #   Units
    # 3   Unit 1
    # 4     Bedroom 1
    # 5     Bathroom 1
    # 6     Bathroom 2
    # 7     Bathroom 3
    # 8     Kitchenette
    # 9 Review

    let!(:property) { create(:property, kind: :multifamily) }

    let!(:unit) do
      create(:unit, name: 'Unit 1', property: property).tap do |unit|
        unit.floorplan.update!(bedrooms: 1, bathrooms: 2.5)
      end
    end

    let!(:common_room) do
      create(:room,
             kind: :living_room,
             nickname: 'Common',
             property: property,
             unit: nil)
    end

    let!(:kitchenette) do
      create(:room,
             kind: :kitchen,
             nickname: 'Kitchenette',
             property: property,
             unit: unit)
    end

    let!(:inspection) do
      create(:inspection, property: property).tap do |inspection|
        inspection.records.create!(target: property)
        inspection.records.create!(target: unit)
        inspection.template.questions.bathroom.create!(
          kind: :free_response,
          prompt: 'Bathroom Question'
        )
        inspection.template.questions.bedroom.create!(
          kind: :free_response,
          prompt: 'Bedroom Question'
        )
        inspection.template.questions.room.create!(
          room_kind: :living_room,
          kind: :free_response,
          prompt: 'Living Room Question'
        )
        inspection.template.questions.room.create!(
          room_kind: :kitchen,
          kind: :free_response,
          prompt: 'Kitchen Question'
        )
      end
    end

    describe '#pages' do
      subject(:pages) { wizard.pages }

      it 'has the property page as the first page' do
        page = subject.first

        expect(page.property).to eq(property)
        expect(page.depth).to eq(0)
        expect(page.title).to eq(property.name)
        expect(page.subtitle).to eq('General Questions')
        expect(page.previous_page).to be_nil
        expect(page.next_page.room).to eq(common_room)
      end

      it 'has the common room as the second page' do
        page = subject.second

        expect(page.room).to eq(common_room)
        expect(page.depth).to eq(1)
        expect(page.title).to eq(common_room.nickname)
        expect(page.subtitle).to eq('Common Living Room')
        expect(page.previous_page.property).to eq(property)
        expect(page.next_page.title).to eq('Unit 1')
      end

      it 'has the unit page as the third page' do
        page = subject.third

        expect(page.unit).to eq(unit)
        expect(page.title).to eq(unit.name)
        expect(page.subtitle).to eq('General Questions')
        expect(page.depth).to eq(1)
      end

      it 'has a bedroom page as the fourth page' do
        page = subject.fourth

        expect(page.unit).to eq(unit)
        expect(page.index).to eq(0)
        expect(page.title).to eq('Bedroom 1')
        expect(page.depth).to eq(2)
      end

      it 'has a bathroom page as the fith page' do
        page = subject.fifth

        expect(page.unit).to eq(unit)
        expect(page.index).to eq(0)
        expect(page.title).to eq('Bathroom 1')
      end

      it 'has the kitchenette page the eigth page' do
        page = subject[-2]

        expect(page.room).to eq(kitchenette)
        expect(page.title).to eq(kitchenette.nickname)
        expect(page.subtitle).to eq('Unit 1 Kitchen')
      end

      it 'has the review page as the last page' do
        page = subject.last

        expect(page.depth).to eq(0)
        expect(page.title).to eq('Review')
        expect(page.previous_page.room).to eq(kitchenette)
        expect(page.next_page).to be_nil
      end
    end
  end
end
