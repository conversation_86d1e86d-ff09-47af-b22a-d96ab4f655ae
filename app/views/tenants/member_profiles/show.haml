.ui.container
  .ui.info.message
    %p
      Please complete your
      - if current_tenant_is_guardian?
        guardian
      - else
        member
      profile before continuing.

    - if pike_member_profile? || pike_guardian_profile?
      %p
        IMPORTANT: Upon completion of your profile below, you are required to
        add a payment method and set up autopay by setting up a scheduled
        payment.

        %ol
          %li
            Once you select Save at the bottom of the screen, you will go to
            Account > Add Payment Method.
          %li
            Once a payment method is added, navigate to Account > Scheduled
            Payments, and schedule a recurring rent payment each month.
          %li
            You will not receive a lease to sign until you’ve completed these
            steps. Note that only one individual, either the tenant or the
            co-signer, needs to have a payment method on file.

  = form_with model: current_tenant,
    url: tenants_member_profile_path,
    method: :patch,
    class: 'ui form',
    remote: true,
    local: false do |f|

    - f.fields_for :metadata_attributes do |mf|
      - f.fields_for :taxpayer_identification do |tf|
        .ui.basic.vertical.segment
          %h4.ui.header
            - if current_tenant_is_guardian?
              Guardian Information
            - else
              Member Information

          .equal.width.fields
            .disabled.field
              = f.label :first_name
              = f.text_field :first_name

            .disabled.field
              = f.label :last_name
              = f.text_field :last_name

            - unless adpi_member_profile?
              .field
                = mf.label :nickname
                = mf.text_field :nickname,
                  name: 'tenant[metadata_attributes][data][nickname]',
                  value: f.object.meta(:nickname)

          .equal.width.fields
            .disabled.field
              = f.label :email, dphie_member_profile? ? 'University Email' : 'Email'
              = f.email_field :email

            - if sae_member_profile?
              .required.field
                = mf.label :university_email
                = mf.email_field :university_email,
                  name: 'tenant[metadata_attributes][data][university_email]',
                  value: f.object.meta(:university_email)

            .required.field
              = f.label :phone
              = f.text_field :phone

            .required.field
              = f.label :date_of_birth, 'Date of Birth'
              = f.semantic_date_field :date_of_birth

          - unless kappa_sig_gamma_rho_profile?
            .equal.width.fields
              - if xo_msu_profile?
                .required.field
                  = mf.label :drivers_license_number
                  = mf.text_field :drivers_license_number,
                    name: 'tenant[metadata_attributes][data][drivers_license_number]',
                    value: f.object.meta(:drivers_license_number)

              - if pike_profile?
                .required.field
                  = mf.label :drivers_license_number
                  = mf.text_field :drivers_license_number,
                    name: 'tenant[metadata_attributes][data][drivers_license_number]',
                    value: f.object.meta(:drivers_license_number)
              - else
                - unless zbt_rho_member_profile? || adpi_member_profile?
                  .required.field
                    = mf.label :student_id_number, 'Student ID Number'
                    = mf.text_field :student_id_number,
                      name: 'tenant[metadata_attributes][data][student_id_number]',
                      value: f.object.meta(:student_id_number)

              - if sae_member_profile? || pike_guardian_profile? || xo_berkeley_profile? || dphie_member_profile? || trisigma_member_profile? || zbt_rho_member_profile? || barrister_member_profile? || adpi_member_profile?
                .required.field
                  = tf.label :tin, 'Social Security Number'
                  = tf.password_field :tin, value: f.object&.tin

              - if sae_member_profile? || kappa_sig_gamma_omicron_member_profile?
                .required.field
                  = mf.label :year_in_school
                  = mf.semantic_dropdown :year_in_school,
                    [['Select', nil],
                    ['Freshman', 'freshman'],
                    ['Sophomore', 'sophomore'],
                    ['Junior', 'junior'],
                    ['Senior', 'senior']],
                    {},
                    name: 'tenant[metadata_attributes][data][year_in_school]'

              - if kappa_sig_gamma_omicron_member_profile?
                .required.field
                  = mf.label :lived_in_before
                  = mf.semantic_dropdown :lived_in_before,
                    [['Select', nil],
                    ['Yes', 'yes'],
                    ['No', 'no']],
                    {},
                    name: 'tenant[metadata_attributes][data][lived_in_before]'

          - if zbt_rho_member_profile?
            .ui.basic.vertical.segment
              .ui.info.message
                %p
                  What is your preferred Payment Option? Please note, should
                  payment for the Annual of Semester plan not be made by the
                  due date, then the payment option will be changed to Monthly.

              .required.field
                = mf.label :preferred_payment_option
                = mf.semantic_dropdown :preferred_payment_option,
                  [['Select', nil], ['Annual', 'Annual'], ['Semester', 'Semester'], ['Monthly', 'Monthly']],
                  {},
                  data: { options: { placeholder: 'Select' } },
                  name: 'tenant[metadata_attributes][data][preferred_payment_option]'

              .required.field
                = mf.label :interested_in_parking
                = mf.semantic_dropdown :interested_in_parking,
                  [['Select', nil], ['Yes', 'Yes'], ['No', 'No']],
                  {},
                  data: { options: { placeholder: 'Select' } },
                  name: 'tenant[metadata_attributes][data][interested_in_parking]'

          - if barrister_member_profile?
            .required.field
              = mf.label :university, 'What university will you be attending?'
              = mf.text_field :university,
                name: 'tenant[metadata_attributes][data][university]',
                value: f.object.meta(:university)

            .ui.basic.vertical.segment
              %h3.ui.header
                Room Preference Information

              %p
                There are no guarantees of room preference. Please select that you
                understand that you may have a roommate.

              %ul
                %li
                  Singles are available only at a higher rental rate and are
                  dependent on extremely limited availability. Seniority for Singles
                  will be established by GPA / Credit Hours
                %li
                  Doubles are the default at all properties.
                %li
                  Triple rooms are only available at Illinois State, Miami and
                  Missouri.

              .grouped.fields
                .field
                  .ui.checkbox
                    = mf.check_box :barrister_terms, checked: false,
                      name: 'tenant[metadata_attributes][data][barrister_terms]'
                    = mf.label :barrister_terms, 'I Understand'

          - if dphie_member_profile?
            .ui.basic.vertical.segment
              .required.field
                = mf.label :housing_staff_text_or_call,
                  'Is it okay for the Housing Staff to text or call you if needed?'
                = mf.semantic_dropdown :housing_staff_text_or_call,
                  [['Select', nil], ['Yes', 'yes'], ['No', 'no']],
                  {},
                  data: { options: { placeholder: 'Select' } }

              .required.field
                = mf.label :room_type
                = mf.semantic_dropdown :room_type,
                  ['Single',
                  'Single+ (Delta Eta and Delta Xi Only)',
                  'Double',
                  'Quad',
                  'Triple',
                  'Cold Dorm',
                  'Double (Fall Semester Only)',
                  'Double (Spring Semester Only)',
                  'Not Living In House'],
                  {},
                  name: 'tenant[metadata_attributes][data][room_type]'

          - if xo_eta_kappa_profile? && !current_tenant_is_guardian?
            .field
              = mf.label :dietary_requirements do
                Do you have any dietary requirements or preferences, such as gluten-free or vegetarian diet? If yes, please explain.
              = mf.text_area :dietary_requirements, rows: 2,
                name: 'tenant[metadata_attributes][data][dietary_requirements]'

            .required.field
              = mf.label :financial_aid_reimbursement do
                Will you be waiting for a financial aid reimbursement to make your first payment?
              = mf.semantic_dropdown :financial_aid_reimbursement,
                ['No', 'Yes'],
                {},
                name: 'tenant[metadata_attributes][data][financial_aid_reimbursement]'

          - if trisigma_member_profile?
            .two.required.fields
              .field
                = mf.label :anticipated_graduation_date
                = mf.semantic_dropdown :anticipated_graduation_date,
                  [['Select', nil],
                  'Fall 2023', 'Spring 2024', 'Fall 2024', 'Spring 2025', 'Fall 2025', 'Spring 2026', 'Fall 2026', 'Spring 2027', 'Fall 2027', 'Spring 2028'],
                  {},
                  class: 'ui search selection dropdown',
                  name: 'tenant[metadata_attributes][data][expected_graduation]',
                  data: { options: { placeholder: 'Select' } }

              .field
                = mf.label :major
                = mf.text_field :major,
                  name: 'tenant[metadata_attributes][data][major]'

          - if xo_member_profile? && xo_berkeley_profile?
            .ui.basic.vertical.segment.school-address-fields
              %h4.ui.header
                Your Address at Berkeley (Only if Not Living in House)
              .two.fields
                .required.field
                  = mf.label :berkeley_address_line_one, 'Street Address Line One'
                  = mf.text_field :berkeley_address_line_one,
                    name: 'tenant[metadata_attributes][data][berkeley_address_line_one]',
                    value: f.object.meta(:berkeley_address_line_one)
                .field
                  = mf.label :berkeley_address_line_two, 'Street Address Line Two'
                  = mf.text_field :berkeley_address_line_two,
                    name: 'tenant[metadata_attributes][data][berkeley_address_line_two]',
                    value: f.object.meta(:berkeley_address_line_two)
              .three.fields
                .required.field
                  = mf.label :berkeley_address_city, 'City'
                  = mf.text_field :berkeley_address_city,
                    name: 'tenant[metadata_attributes][data][berkeley_address_city]',
                    value: f.object.meta(:berkeley_address_city)
                .required.field
                  = mf.label :berkeley_address_region, 'State'
                  = mf.text_field :berkeley_address_region,
                    name: 'tenant[metadata_attributes][data][berkeley_address_region]',
                    value: f.object.meta(:berkeley_address_region)
                .required.field
                  = mf.label :berkeley_address_postal_code, 'Zip Code'
                  = mf.text_field :berkeley_address_postal_code,
                    name: 'tenant[metadata_attributes][data][berkeley_address_postal_code]',
                    value: f.object.meta(:berkeley_address_postal_code)
          - elsif xo_member_profile? && xo_msu_profile?
            .ui.basic.vertical.segment.school-address-fields
              %h4.ui.header
                Your Address at Michigan State
              .two.fields
                .required.field
                  = mf.label :michigan_state_address_line_one, 'Street Address Line One'
                  = mf.text_field :michigan_state_address_line_one,
                    name: 'tenant[metadata_attributes][data][michigan_state_address_line_one]',
                    value: f.object.meta(:michigan_state_address_line_one)
                .field
                  = mf.label :michigan_state_address_line_two, 'Street Address Line Two'
                  = mf.text_field :michigan_state_address_line_two,
                    name: 'tenant[metadata_attributes][data][michigan_state_address_line_two]',
                    value: f.object.meta(:michigan_state_address_line_two)
              .three.fields
                .required.field
                  = mf.label :michigan_state_address_city, 'City'
                  = mf.text_field :michigan_state_address_city,
                    name: 'tenant[metadata_attributes][data][michigan_state_address_city]',
                    value: f.object.meta(:michigan_state_address_city)
                .required.field
                  = mf.label :michigan_state_address_region, 'State'
                  = mf.text_field :michigan_state_address_region,
                    name: 'tenant[metadata_attributes][data][michigan_state_address_region]',
                    value: f.object.meta(:michigan_state_address_region)
                .required.field
                  = mf.label :michigan_state_address_postal_code, 'Zip Code'
                  = mf.text_field :michigan_state_address_postal_code,
                    name: 'tenant[metadata_attributes][data][michigan_state_address_postal_code]',
                    value: f.object.meta(:michigan_state_address_postal_code)

          - if xo_member_profile? && !xo_berkeley_profile?
            .ui.yellow.message
              .header
                Food Allergies or Dietary Conditions
              %p
                If you select the option below, you must send medical
                documentation supporting the allergy(s) / condition to
                #{brand.name} via the messages tab on your member portal.

            .field
              .ui.checkbox
                = f.check_box :food_allergy
                = f.label :food_allergy,
                  'I have a medically documented food allergy or dietary condition'

          - if xo_sigma_beta_member_profile?
            .ui.yellow.message
              .header
                Medical Information
              %p
                Are there any medical issues we need to know baout? If you are
                taken to the hospital, is there medical information we need to
                give them?

            .field
              = mf.label :health_conditions, 'Health Conditions'
              = mf.text_area :health_conditions, rows: 2,
                name: 'tenant[metadata_attributes][data][health_conditions]'

          - if pike_member_profile?
            .field
              .ui.checkbox
                = f.check_box :requested_parking
                = f.label :requested_parking,
                  'I would like to request a parking spot'

          - if sigep_azbeta_member_profile?
            .ui.info.message
              .header Meal Plans
              %p
                %b Full Meal Plan
                includes Mon-Fri lunch and dinner. Cold bar (yogurt, fruit, etc.) in the morning available for purchase a la carte.
              %p
                %b Partial Meal Plan
                includes 5 meals per week. These will be the same meal times per week, and we will need your set schedule in advance of starting service.
              %p
                %b Chapter Dinner Meal Plan
                includes dinner before each chapter meeting for the semester.

              %p
                By selecting a meal plan, you agree to pay the amount set forth above in accordance with the Membership Agreement.

            .field
              = f.label :meal_plan
              = f.semantic_dropdown :meal_plan,
                [['No Meal Plan', 'No Meal Plan'],
                ['Full Meal Plan ($1,750.00 for Fall Semester)', 'Full Meal Plan'],
                ['Partial Meal Plan ($925.00 for Fall Semester)', 'Partial Meal Plan'],
                ['Chapter Dinner Meal Plan ($200.00 for Fall Semester)', 'Chapter Dinner Meal Plan']],
                {},
                class: 'ui search selection dropdown',
                name: 'tenant[metadata_attributes][data][meal_plan]'

          - if !current_tenant_is_guardian? && (xo_berkeley_profile? || xo_msu_profile? || xo_beta_beta_profile? || xo_psi_profile?)
            .equal.width.fields
              .required.field
                = f.label :pledge_class
                = f.semantic_dropdown :pledge_class,
                  [['Select', nil],
                  'Fall 2018', 'Spring 2019', 'Fall 2019', 'Spring 2020', 'Fall 2020', 'Spring 2021', 'Fall 2021', 'Spring 2022', 'Fall 2022', 'Spring 2023', 'Fall 2023', 'Spring 2024'],
                  {},
                  class: 'ui search selection dropdown',
                  name: 'tenant[metadata_attributes][data][pledge_class]',
                  data: { options: { placeholder: 'Select' } }

              .required.field
                = f.label :expected_graduation
                = f.semantic_dropdown :expected_graduation,
                  [['Select', nil],
                  'Fall 2023', 'Spring 2024', 'Fall 2024', 'Spring 2025', 'Fall 2025', 'Spring 2026', 'Fall 2026', 'Spring 2027', 'Fall 2027', 'Spring 2028'],
                  {},
                  class: 'ui search selection dropdown',
                  name: 'tenant[metadata_attributes][data][expected_graduation]',
                  data: { options: { placeholder: 'Select' } }

            - unless xo_msu_profile? || xo_psi_profile?
              .required.field
                = f.label :abroad, 'Do you plan to go abroad?'
                = f.semantic_dropdown :abroad,
                  [['Select', nil],
                  'No', 'Fall 2023', 'Spring 2024', 'Fall 2024', 'Spring 2025', 'Fall 2025', 'Spring 2026', 'Fall 2026', 'Spring 2027'],
                  {},
                  class: 'ui search selection dropdown',
                  name: 'tenant[metadata_attributes][data][abroad]',
                  data: { options: { placeholder: 'Select' } }

              - unless xo_beta_beta_profile? || xo_psi_profile?
                .required.grouped.fields
                  %label Do you plan on paying in full or installments?

                  .field
                    .ui.radio.checkbox
                      = f.radio_button :pay_in_installments, 'no',
                        name: 'tenant[metadata_attributes][data][pay_in_installments]',
                        checked: true
                      = f.label :pay_in_installments, 'In Full'

                  .field
                    .ui.radio.checkbox
                      = f.radio_button :pay_in_installments, 'yes',
                        name: 'tenant[metadata_attributes][data][pay_in_installments]'
                      = f.label :pay_in_installments, 'Installments'

            - if xo_msu_profile?
              .required.field
                = f.label :abroad, 'Will you be using student loans to pay for your upcoming housing fees?'
                = f.semantic_dropdown :using_student_loans_for_housing_fees,
                  [['Select', nil], 'Yes', 'No'],
                  {},
                  class: 'ui search selection dropdown',
                  name: 'tenant[metadata_attributes][data][using_student_loans_for_housing_fees]',
                  data: { options: { placeholder: 'Select' } }

        - if enter_guardian?
          .ui.basic.vertical.segment.guardian-fields
            %h4.ui.header
              Parent or Guardian

            .ui.info.message
              %p
                - if adpi_member_profile?
                  Adding a parent or guardian will provide them access to Revela,
                  including the ability to see your balance due, submit payments,
                  and be copied on automated emails regarding billing and payments.
                - else
                  Your parent or guardian will be invited to login to your Member
                  Portal and have access to view and pay charges.

                - if xo_berkeley_profile?
                  This person will co-sign your agreement.

            = f.fields_for :guardian, @guardian do |pf|
              .equal.width.fields{ class: class_names(required: guardian_required?) }
                - if sigep_azbeta_member_profile?
                  .field
                    = mf.label :relation
                    = mf.text_field :relation,
                      name: 'tenant[metadata_attributes][data][guardian_relation]'

                .field
                  = pf.label :first_name
                  = pf.text_field :first_name

                .field
                  = pf.label :last_name
                  = pf.text_field :last_name

              .two.fields{ class: class_names(required: guardian_required?) }
                .field
                  = pf.label :email
                  = pf.email_field :email

                .field
                  = pf.label :phone
                  = pf.text_field :phone

        - if adpi_member_profile?
          .ui.basic.vertical.segment.school-address-fields
            %h4.ui.header
              Your Address At School
            .two.fields
              .required.field
                = mf.label :school_address_line_one, 'Street Address Line One'
                = mf.text_field :school_address_line_one,
                  name: 'tenant[metadata_attributes][data][school_address_line_one]',
                  value: f.object.meta(:school_address_line_one)
              .field
                = mf.label :school_address_line_two, 'Street Address Line Two'
                = mf.text_field :school_address_line_two,
                  name: 'tenant[metadata_attributes][data][school_address_line_two]',
                  value: f.object.meta(:school_address_line_two)
            .three.fields
              .required.field
                = mf.label :school_address_city, 'City'
                = mf.text_field :school_address_city,
                  name: 'tenant[metadata_attributes][data][school_address_city]',
                  value: f.object.meta(:school_address_city)
              .required.field
                = mf.label :school_address_region, 'State'
                = mf.text_field :school_address_region,
                  name: 'tenant[metadata_attributes][data][school_address_region]',
                  value: f.object.meta(:school_address_region)
              .required.field
                = mf.label :school_address_postal_code, 'Zip Code'
                = mf.text_field :school_address_postal_code,
                  name: 'tenant[metadata_attributes][data][school_address_postal_code]',
                  value: f.object.meta(:school_address_postal_code)

        .ui.basic.vertical.segment.forwarding-address-fields
          %h4.ui.header
            - if dphie_member_profile? || (!current_tenant_is_guardian? && xo_beta_beta_profile?) || kappa_sig_gamma_omicron_profile?
              Home Mailing Address (not the chapter house address)
            - else
              Permanent Home Address

          - @default_address = f.object.forwarding_address
          = render partial: 'shared/address_fields',
            locals: { f: f }

        - if xo_sigma_beta_member_profile?
          .ui.very.padded.basic.vertical.segment
            %h4.ui.header
              Primary Emergency Contact

          :ruby
            emergency_contact_fields = %i[
              first_name last_name phone
              email street_address city
              state zip_code relationship
            ]

          - emergency_contact_fields.in_groups_of(3).each do |group|
            .three.required.fields
              - group.each do |field|
                .field
                  = mf.label :"emergency_contact_#{field}", field.to_s.titleize
                  = mf.text_field :"emergency_contact_#{field}",
                    name: "tenant[metadata_attributes][data][emergency_contact_#{field}]"

        - if dphie_member_profile? || (!current_tenant_is_guardian? && xo_beta_beta_profile?)
          - unless xo_beta_beta_profile?
            -# Primary Emergency Contact
            .ui.very.padded.basic.vertical.segment
              %h3.ui.header
                Primary Emergency Contact

              .three.fields
                .required.field
                  = mf.label :emergency_contact_first_name, 'First Name'
                  = mf.text_field :emergency_contact_first_name,
                    name: 'tenant[metadata_attributes][data][emergency_contact_first_name]'

                .field
                  = mf.label :emergency_contact_middle_name, 'Middle Name'
                  = mf.text_field :emergency_contact_middle_name,
                    name: 'tenant[metadata_attributes][data][emergency_contact_middle_name]'

                .required.field
                  = mf.label :emergency_contact_last_name, 'Last Name'
                  = mf.text_field :emergency_contact_last_name,
                    name: 'tenant[metadata_attributes][data][emergency_contact_last_name]'

              .three.fields
                .field
                  = mf.label :emergency_contact_nickname, 'Informal First Name'
                  = mf.text_field :emergency_contact_nickname,
                    name: 'tenant[metadata_attributes][data][emergency_contact_nickname]'

                .required.field
                  = mf.label :emergency_contact_phone, 'Phone'
                  = mf.text_field :emergency_contact_phone,
                    name: 'tenant[metadata_attributes][data][emergency_contact_phone]'

                .required.field
                  = mf.label :emergency_contact_relation, 'Relation To Member'
                  = mf.text_field :emergency_contact_relation,
                    name: 'tenant[metadata_attributes][data][emergency_contact_relation]'

              .required.field
                = mf.label :emergency_contact_permission_to_contact,
                  'Do we have permission to contact and share your health / emergency information with your emergency contact?'
                = mf.semantic_dropdown :emergency_contact_permission_to_contact,
                  [['Select', nil], ['Yes', 'yes'], ['No', 'no']],
                  {},
                  name: 'tenant[metadata_attributes][data][emergency_contact_permission_to_contact]',
                  data: { options: { placeholder: 'Select' } }

          -# Medical Information
          .ui.very.padded.basic.vertical.segment
            %h3.ui.header
              Medical Information

            %p
              This information will remain confidential and strictly used in an
              emergency situation where first aid is required. If you are not
              comfortable providing this information, you may choose to skip this
              section. This information will not impact in any way your ability to
              live in the house.

            .field
              = mf.label :food_allergies, 'Allergies, if any, to FOOD'
              = mf.text_area :food_allergies, rows: 2,
                name: 'tenant[metadata_attributes][data][food_allergies]'

            .field
              = mf.label :non_food_allergies, 'Allergies, if any, to NON-FOOD'
              = mf.text_area :non_food_allergies, rows: 2,
                name: 'tenant[metadata_attributes][data][non_food_allergies]'

            .field
              = mf.label :special_health_conditions,
                'Special Health Conditions (asthma, diabetes, etc.)'
              = mf.text_area :special_health_conditions, rows: 2,
                name: 'tenant[metadata_attributes][data][special_health_conditions]'

            .field
              = mf.label :special_medications_or_treatments,
                'Special medications or treatments (insulin, inhaler, etc.)'
              = mf.text_area :special_medications_or_treatments, rows: 2,
                name: 'tenant[metadata_attributes][data][special_medications_or_treatments]'

          - unless xo_beta_beta_profile?
            -# Payment Plan
            .ui.very.padded.basic.vertical.segment
              %h3.ui.header
                Payment Plan / Financial Aid

              .required.field
                = mf.label :dphie_payment_plan,
                  'Will you be using the Delta Phi Epsilon payment plan program? (information below)'
                = mf.semantic_dropdown :dphie_payment_plan,
                  [['Select', nil], ['Yes', 'yes'], ['No', 'no']],
                  {},
                  name: 'tenant[metadata_attributes][data][dphie_payment_plan]',
                  data: { options: { placeholder: 'Select' } }

              %p
                %i
                  Payment plans require the payment of a $50 payment plan
                  administrative fee each semester.

              %table.ui.very.basic.stackable.table
                %tbody
                  %tr
                    %td Fall Semester Payment Due Dates:
                    %td Spring Semester Payment Due Dates:
                  %tr
                    %td June 1, 2023
                    %td November 2, 2023
                  %tr
                    %td July 1, 2023
                    %td December 2, 2023
                  %tr
                    %td August 1, 2023 or before move date, whichever one comes first.
                    %td January 2, 2024 or before move date, whichever one comes first.

              .required.field
                = mf.label :financial_aid,
                  'Will you be using financial aid?'
                = mf.semantic_dropdown :financial_aid,
                  [['Select', nil], ['Yes', 'yes'], ['No', 'no']],
                  {},
                  name: 'tenant[metadata_attributes][data][financial_aid]',
                  data: { options: { placeholder: 'Select' } }

              .required.field
                = mf.label :payment_plan_529,
                  'Will you be using a 529 payment plan?'
                = mf.semantic_dropdown :payment_plan_529,
                  [['Select', nil], ['Yes', 'yes'], ['No', 'no']],
                  {},
                  name: 'tenant[metadata_attributes][data][payment_plan_529]',
                  data: { options: { placeholder: 'Select' } }

              .required.field
                = mf.label :florida_prepaid_account,
                  'Florida Prepaid Account (University of Florida Students Only)'
                = mf.semantic_dropdown :florida_prepaid_account,
                  [['Select', nil], ['Yes', 'yes'], ['No', 'no']],
                  {},
                  name: 'tenant[metadata_attributes][data][florida_prepaid_account]',
                  data: { options: { placeholder: 'Select' } }

        .ui.basic.vertical.segment
          .ui.checkbox
            = f.check_box :agree_to_sms, checked: true
            = f.label :agree_to_sms,
              t('views.tenant.agree_to_sms',
              customer_name: Customer.current.name)

        .ui.basic.vertical.segment
          .ui.error.message

          .field
            = f.submit 'Save', class: 'ui primary submit button'

:javascript
  $('.ui.checkbox').checkbox();
