require 'rails_helper'

RSpec.describe 'maintenance estimates index', :js,
               capybara_login: :property_manager do
  before { visit maintenance_estimates_path }

  describe 'filtering' do
    describe 'owner approval' do
      let!(:estimate) { create(:maintenance_estimate) }

      scenario 'by unsent' do
        filter_index 'Owner Approval', with: 'Unsent'

        expect(page).to have_content(estimate.service_area.name)
      end

      scenario 'by pending' do
        create(:approvals_request, approvable: estimate)

        filter_index 'Owner Approval', with: 'Pending'

        expect(page).to have_content(estimate.service_area.name)
      end

      scenario 'by approved' do
        create(:approvals_request, :approved, approvable: estimate)

        filter_index 'Owner Approval', with: 'Approved'

        expect(page).to have_content(estimate.service_area.name)
      end

      scenario 'by rejected' do
        create(:approvals_request, :rejected, approvable: estimate)

        filter_index 'Owner Approval', with: 'Rejected'

        expect(page).to have_content(estimate.service_area.name)
      end
    end
  end
end
