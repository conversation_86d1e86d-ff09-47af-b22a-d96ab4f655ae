= render ::ModalComponent.new(id: :countersigner_modal,
  title: 'Send For Signatures',
  options: { autofocus: false,
  form: :countersigner_form }) do

  = form_with id: :countersigner_form,
    class: 'ui form',
    url: electronic_signable.electronic_signature_request.request_signatures_path,
    method: :post,
    local: false,
    remote: true do |f|

    .field
      .ui.info.message
        Please review the document carefully before sending it for signatures.
      
    .required.field
      = f.label :countersigner_id, 'Countersigner'
      = f.semantic_dropdown :countersigner_id,
        options_for_select(countersigners, primary_countersigner&.id),
        {},
        class: 'ui search dropdown selection'
