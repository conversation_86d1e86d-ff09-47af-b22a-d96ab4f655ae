WITH deposit_activity AS (
  SELECT
    plutus_entries.id,
    plutus_entries.lease_membership_id,
    credit_accounts.type AS credit_type,
    debit_accounts.type AS debit_type,
    credit_amounts.amount AS credit_amount,
    debit_amounts.amount AS debit_amount,
    invoices.post_date AS date,
    invoice_total_payments.payment AS payment_amount

  FROM
    plutus_entries

  -- The credit and debit amounts, used for joining accounts
  LEFT JOIN
    plutus_amounts credit_amounts
      ON credit_amounts.entry_id = plutus_entries.id
        AND credit_amounts.type = 'Plutus::CreditAmount'
  LEFT JOIN
    plutus_amounts debit_amounts
      ON debit_amounts.entry_id = plutus_entries.id
        AND debit_amounts.type = 'Plutus::DebitAmount'

  -- The credit and debit accounts, used for fetching type
  LEFT JOIN
    plutus_accounts credit_accounts
      ON credit_amounts.account_id = credit_accounts.id
  LEFT JOIN
    plutus_accounts debit_accounts
      ON debit_amounts.account_id = debit_accounts.id

  -- Tenant invoice payment amounts for cash basis current payment
  LEFT JOIN
    invoices
      ON invoices.id = plutus_entries.commercial_document_id
        AND plutus_entries.commercial_document_type = 'Invoice'
        AND invoices.buyer_type = 'Tenant'
  LEFT JOIN
    invoice_total_payments
      ON invoice_total_payments.invoice_id = invoices.id

  -- Only deposit transactions
  WHERE
    credit_accounts.id IN (SELECT security_deposit_account_id FROM configurations)
      OR debit_accounts.id IN (SELECT security_deposit_account_id FROM configurations)
)

SELECT
  lease_membership_id,

  min(date) as date,

  -- Increases to sdl were charged to the tenant
  sum(
    CASE WHEN debit_type = 'Plutus::Asset' THEN credit_amount ELSE 0 END
  ) AS charged_cents,

  -- Decreases to sdl that decrease AR are retained
  sum(
    CASE WHEN credit_type = 'Plutus::Asset' THEN debit_amount ELSE 0 END
  ) AS deducted_cents,

  -- Decreases to sdl are returned to the tenant
  sum(
    CASE WHEN
      debit_type = 'Plutus::Liability' AND credit_type = 'Plutus::Liability'
    THEN debit_amount ELSE 0 END
  ) AS returned_cents,

  -- Sum of security deposit invoice payments is paid
  coalesce(
    sum(
      payment_amount
    ),
    0
  ) AS paid_cents

FROM
  deposit_activity

WHERE
  lease_membership_id IS NOT NULL

GROUP BY
  lease_membership_id
;
