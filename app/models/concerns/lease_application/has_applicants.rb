module LeaseApplication::HasApplicants
  extend ActiveSupport::Concern

  included do
    has_many :lease_application_memberships, dependent: :destroy
    has_many :tenants, through: :lease_application_memberships
    # rubocop:disable Rails/HasManyOrHasOneDependent
    has_one :primary_lease_application_membership,
            -> { where(primary: true) },
            class_name: 'LeaseApplicationMembership',
            inverse_of: :lease_application
    # rubocop:enable Rails/HasManyOrHasOneDependent
    has_one :primary_tenant, through: :primary_lease_application_membership,
                             source: :tenant

    validate :one_primary_membership, if: :submitted?
  end

  def applicants
    (fields['applicants'] || [{ email: email, kind: 'primary_applicant' }]).map.with_index do |a, i|
      LeaseApplication::Applicant.new(
        a.merge(id: i + 1, lease_application_id: id)
      )
    end
  end

  def applicants_attributes=(attrs)
    new_form = fields

    attrs.each do |id, values|
      new_form['applicants'].find { |a| a['id'].to_s == id.to_s }&.merge!(values)
    end

    assign_attributes(form: new_form)
  end

  private

  def one_primary_membership
    return if lease_application_memberships.one?(&:primary?)

    errors.add(:tenants, 'must have one primary applicant')
  end
end
