class NewProperty::UnitsPage < Reform::Form
  collection :floorplans do
    property :name, default: 'Unit 1'
    property :bedrooms
    property :bathrooms
    property :square_feet
    property :price
    property :_destroy, virtual: true

    validates :name, presence: true

    delegate :new_record?, :marked_for_destruction?, to: :model
  end

  def self.reflect_on_association(sym)
    ::Property.reflect_on_association(sym)
  end
end
