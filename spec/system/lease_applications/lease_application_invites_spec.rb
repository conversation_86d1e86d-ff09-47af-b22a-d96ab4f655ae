require 'rails_helper'

RSpec.describe 'receiving lease application invites', :js, capybara_login: :property_manager do
  let(:email) { '<EMAIL>' }

  describe 'opening a lease application email' do
    before do
      LeaseApplicationsMailer.send_application_invite(
        application
      ).deliver_now

      open_email(email)

      current_email.click_link 'Start Application'
    end

    context 'a new application' do
      let(:application) { create(:lease_application, :invite, email: email) }

      scenario 'a user sees the lease application' do
        expect(page).to have_content 'Rental Application'
        expect(page).to have_content application.property.name
      end
    end

    context 'an expired application' do
      let(:application) do
        create(:lease_application,
               :invite, expires_at: 3.days.ago, email: email)
      end

      scenario 'a user sees a notice about an expired invite' do
        expect(page).to have_content(/has expired/i)
      end
    end
  end

  describe 'creating an invite' do
    let(:guest_card) { create(:guest_card) }
    let(:tenant) { guest_card.tenant }

    scenario 'sending an invitation' do
      visit new_leasing_lease_application_invite_path(lead_id: tenant.id)

      perform_enqueued_jobs do
        click_on 'Send Application'
      end

      la = LeaseApplication.last
      expect(la.lead).to eq tenant
      expect(tenant.reload.leasing_agent).to eq manager

      open_email tenant.email

      expect(current_email.to).to eq [tenant.email]
      expect(current_email.subject).to eq \
        "Lease Application to #{guest_card.property.name}"
    end
  end
end
