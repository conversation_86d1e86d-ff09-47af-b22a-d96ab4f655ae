class Accounting::FeeManagement::MonthEndReview::SummaryTable
  attr_reader :month_end_review

  def initialize(month_end_review:)
    @month_end_review = month_end_review
  end

  def rows(&block)
    cached = Redis.instance { |r| r.get(month_end_review.cache_key).present? }

    Redis.instance do |r|
      r.set(
        month_end_review.cache_key, true,
        ex: Accounting::FeeManagement::MonthEndReview::CACHE_EXPIRE
      )
    end

    if cached
      [*activity_rows, total_row].each(&block)
    else
      Plutus.in_cash_basis(journal_cache_hint: entity) do
        month_end_review.suggested_property_transfers

        [*activity_rows, total_row].each(&block)
      end
    end
  end

  private

  delegate :entity, to: :month_end_review
  delegate :properties, to: :entity

  def activity_rows
    # TODO: enable or remove [*property_rows, entity_row]
    property_rows
  end

  def property_rows
    @property_rows ||= month_end_review.property_rows
  end

  def entity_row
    @entity_row ||= OpenStruct.new(
      text: 'Entity Only',
      link: nil,
      beginning_balance: Money.zero,
      cash_in: Money.zero,
      cash_out: Money.zero,
      disbursement: Money.zero,
      ending_balance: Money.zero,
      net_balance: Money.zero
    )
  end

  def total_row
    @total_row ||= begin
      empty = OpenStruct.new(
        beginning_balance: Money.zero,
        cash_in: Money.zero,
        cash_out: Money.zero,
        reserved: Money.zero,
        disbursement: Money.zero,
        ending_balance: Money.zero,
        net_balance: Money.zero
      )

      activity_rows.reduce(empty) do |total, row|
        OpenStruct.new(
          text: 'Total',
          link: nil,
          beginning_balance: total.beginning_balance + row.beginning_balance,
          cash_in: total.cash_in + row.cash_in,
          cash_out: total.cash_out + row.cash_out,
          reserved: total.reserved + row.reserved,
          disbursement: total.disbursement + row.disbursement,
          ending_balance: total.ending_balance + row.ending_balance,
          net_balance: total.net_balance + row.net_balance
        )
      end
    end
  end
end
