require 'rails_helper'

RSpec.describe Inspectify::Order::Create do
  # Inspectify validates addresss
  let(:address) do
    create(
      :address,
      line_one: '6001 Cass Ave',
      line_two: 'Flr 5',
      city: 'Detroit',
      region: 'MI',
      postal_code: '48202',
      country: 'US'
    )
  end

  let(:inspectify_vendor) do
    inspectify_vendor = create(:vendor, name: 'Inspectify')
    Inspectify::VendorToken.create!(vendor: inspectify_vendor,
                                    api_token: '123abc')

    inspectify_vendor
  end

  let(:inspectify_order) do
    inspection_report = create(:inspection_report,
                               assigned_to: inspectify_vendor,
                               due_date: 3.days.from_now,
                               message: Faker::JapaneseMedia::StudioGhibli.character)

    inspection_report.property.update!(address: address)

    inspection_report.active_inspectify_order
  end

  it 'creates a new order on inspectify', :vcr do
    result = described_class.call(inspectify_order)

    expect(result).to be_successful
    inspectify_order.reload
    expect(inspectify_order.inspectify_order_id).to eq('123')
    expect(inspectify_order.inspectify_status).to eq('requested')
    inspection = inspectify_order.inspection_report
    expect(inspection.status).to eq('requested')
    latest_activity = inspection.activities.last
    expect(latest_activity.kind).to eq('inspection_opened')
  end

  describe 'when the Inspectify::Order already exists' do
    context 'when the inspectify_order_id is nil' do
      it 'sends a create order request but receives same inspectify_order_id', :vcr do
        first_result = described_class.call(inspectify_order)

        expect(first_result).to be_successful
        expect(inspectify_order.reload.inspectify_order_id).to eq('123')

        inspectify_order.update!(inspectify_order_id: nil)

        second_result = described_class.call(inspectify_order)

        expect(second_result).to be_successful
        expect(inspectify_order.reload.inspectify_order_id).to eq('123')
      end
    end

    context 'when the inspectify_order_id exists' do
      it 'does not send a create order request to inspectify', :vcr do
        first_result = described_class.call(inspectify_order)

        expect(first_result).to be_successful
        expect(inspectify_order.reload.inspectify_order_id).to eq('123')

        second_result = described_class.call(inspectify_order)

        expect(second_result).to be_successful
        expect(inspectify_order.reload.inspectify_order_id).to eq('123')
      end
    end
  end
end
