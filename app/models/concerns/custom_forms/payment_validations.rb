module CustomForms::PaymentValidations
  extend ActiveSupport::Concern

  included do
    def require_payment_fields?
      form.present? && form.has_payment_automation?
    end

    def require_credit_card_fields?
      require_payment_fields? && use_credit_card?
    end

    def require_ach_fields?
      require_payment_fields? && use_ach?
    end

    def valid_credit_card_expiration_date?
      if credit_card_expiration_date.blank?
        errors.add(:credit_card_expiration_date, "can't be blank")
        return false
      end

      unless credit_card_expiration_date.match?(%r{\A(0[1-9]|1[012])\s?/\s?[0-9]{2}\z})
        errors.add(:credit_card_expiration_date, "is invalid")
        return false
      end

      true
    end

    with_options if: :require_payment_fields? do
      validates :payment_method, presence: true, inclusion: { in: ['cc', 'ach'] }, strict: true
      validates :account_holder_name, presence: true
      validates :billing_address_one, presence: true
      validates :billing_city, presence: true
      validates :billing_state, presence: true, inclusion: { in: Address::US_STATES.values }
      validates :billing_zip_code, presence: true
    end

    with_options unless: :require_payment_fields? do
      validates :payment_method, absence: true, strict: true
      validates :account_holder_name, absence: true, strict: true

      validates :credit_card_number, absence: true, strict: true
      validates :credit_card_expiration_date, absence: true, strict: true
      validates :credit_card_security_code, absence: true, strict: true

      validates :ach_account_number, absence: true, strict: true
      validates :ach_routing_number, absence: true, strict: true
      validates :ach_account_type, absence: true, strict: true

      validates :billing_address_one, absence: true, strict: true
      validates :billing_address_two, absence: true, strict: true
      validates :billing_city, absence: true, strict: true
      validates :billing_state, absence: true, strict: true
      validates :billing_zip_code, absence: true, strict: true
    end

    with_options if: :require_credit_card_fields? do
      validates :credit_card_number, presence: true
      validates :credit_card_security_code, presence: true, numericality: { only_integer: true }, length: { maximum: 4 }
      validate :valid_credit_card_expiration_date?

      validates :ach_account_number, absence: true, strict: true
      validates :ach_routing_number, absence: true, strict: true
      validates :ach_account_type, absence: true, strict: true
    end

    with_options if: :require_ach_fields? do
      validates :ach_account_number, presence: true
      validates :ach_routing_number, presence: true
      validates :ach_account_type, presence: true, inclusion: { in: ['checking', 'savings'] }

      validates :credit_card_number, absence: true, strict: true
      validates :credit_card_expiration_date, absence: true, strict: true
      validates :credit_card_security_code, absence: true, strict: true
    end
  end
end
