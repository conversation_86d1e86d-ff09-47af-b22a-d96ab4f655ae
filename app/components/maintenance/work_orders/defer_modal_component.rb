class Maintenance::WorkOrders::DeferModalComponent < ApplicationComponent
  def initialize(work_order:)
    super()
    @work_order = work_order
  end

  private

  def model
    @work_order.defer || @work_order.build_defer
  end

  def url
    maintenance_ticket_defer_path(@work_order)
  end

  def time_presets
    {
      '7 Days' => 7.days.from_now,
      '30 Days' => 30.days.from_now,
      '90 Days' => 90.days.from_now,
      'Next Week' => Time.zone.today.next_week.beginning_of_week.to_datetime,
      'Next Month' => Time.zone.today.next_month.beginning_of_month.to_datetime
    }.transform_values do |value|
      value.change(sec: 0, usec: 0, hour: 9).to_fs(:calendar_datetime)
    end
  end

  # This is used to prevent incorrect parsing from fomantic ui calendar
  def defer_until_value
    return nil if model.defer_until.blank?

    model.defer_until.to_fs(:calendar_datetime)
  end

  def associated_tenant?
    @work_order.tenant.present?
  end
end
