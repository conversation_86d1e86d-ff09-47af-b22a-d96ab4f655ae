class Reports::V3::Basis::Base
  attr_reader :mapping, :scope, :filters, :grouping, :user, :request

  MAX_ROW_LIMIT = 25_000

  def initialize(mapping:, scope:, filters:, grouping: nil, user: nil, request: nil)
    @mapping = mapping
    @scope = scope
    @filters = filters
    @grouping = grouping
    @user = user
    @request = request
  end

  def rows
    @rows ||= source_rows.map do |object|
      { cells: cells_for_object(object) }
    end
  end

  def sections
    @sections ||= begin
      grouper = self.class.groupings[grouping]

      grouper.group(source_rows).map do |section_values, rows|
        {
          **section_values,
          rows: rows.map do |object|
            { cells: cells_for_object(object) }
          end
        }
      end
    end
  end

  protected

  def source_rows
    ordered(joined(filtered(scoped(relation)))).limit(MAX_ROW_LIMIT)
  end

  def ordered(query)
    query
  end

  def joined(query)
    query
  end

  def scoped(query)
    query
  end

  def filtered(query)
    query
  end

  def self.columns
    @columns ||= {}
  end

  def self.column(key, &block)
    columns[key] = block
  end

  def self.groupings # rubocop:disable Style/TrivialAccessors
    @groupings
  end

  def self.grouping(key, &)
    grouper = Reports::V3::Basis::Grouper.new

    grouper.instance_eval(&)

    (@groupings ||= {})[key] = grouper
  end

  def request_canceled?
    request&.reload&.canceled?
  end

  private

  def cells_for_object(object)
    mapping.keys.map do |key|
      block = self.class.columns[key]

      fail "No column #{key} defined." unless block

      object.instance_exec(filters, &block) || {}
    end
  end
end
