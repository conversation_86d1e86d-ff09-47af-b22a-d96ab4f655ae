class Maintenance::Tickets::CommentsController < MaintenanceController
  include MaintenanceTicketCommentsController

  before_action :set_ticket

  def create
    visibility = :internal

    ActiveRecord::Base.transaction do
      event = @maintenance_ticket.events.comment.create!(
        author: current_property_manager,
        body: params[:comment],
        visibility: visibility
      )

      notify_users(event)

      Attachment.where(id: params[:attachment_ids]).update(parent: event)
    end

    redirect_to maintenance_ticket_path(@maintenance_ticket)
  end

  def destroy
    @maintenance_ticket
      .events.comment
      .where(author: current_property_manager)
      .find(params[:id]).destroy!

    redirect_to maintenance_ticket_path(@maintenance_ticket)
  end

  private

  def set_ticket
    @maintenance_ticket = MaintenanceTicket.find(params[:ticket_id])
  end
end
