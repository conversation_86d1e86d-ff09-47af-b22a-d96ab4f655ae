require 'rails_helper'

RSpec.describe 'property setup building importing', :js,
               capybara_login: :property_manager do
  let!(:property) do
    create(:property, setup: false, kind: :multifamily).tap do |property|
      create(:charge_preset,
             kind: :rent,
             configuration: property.configuration)
    end
  end

  let!(:unit) { create(:unit, property: property, name: 'Unit 1') }

  scenario 'a user imports a rent roll' do
    visit property_setup_units_path(property)

    click_on 'Next'

    attach_file 'Rent Roll', absolute_fixture('importing/rent_roll.xlsx')
    click_on 'Upload'

    expect(page).to have_content('<PERSON>')

    lease = property.leases.first
    expect(lease.unit).to eq(unit)
    expect(lease.start_date).to eq(Date.new(2018, 5, 1))
    expect(lease.end_date).to eq(Date.new(2019, 4, 30))
    expect(lease.amount.format).to eq('$1,650.00')
  end
end
