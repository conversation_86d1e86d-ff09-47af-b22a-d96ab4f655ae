class AddCompanyToAgreementsSimpleAgreements < ActiveRecord::Migration[6.1]
  def change
    add_reference :agreements_simple_agreements, :company, foreign_key: true

    reversible do |direction|
      direction.up do
        Agreements::SimpleAgreement.find_each do |agreement|
          agreement.update!(company: agreement.property.company)
        end
      end
    end

    change_column_null :agreements_simple_agreements, :company_id, false
  end
end
