class PaymentPlan::PresetsQuery
  def initialize(relation = PaymentPlan::Preset.all)
    @relation = relation.extending(Scopes)
  end

  def search
    @relation
  end

  module Scopes
    def currently_available_for_invoice(invoice)
      property = invoice.seller

      return none unless property.is_a?(Property)

      currently_available_at_property(property)
    end

    # For now, all sellers must be the same and it must be a property
    def currently_available_for_invoices(invoices)
      sellers = invoices.map(&:seller).uniq

      return none unless sellers.one?

      property = sellers.first

      return none unless property.is_a?(Property)

      currently_available_at_property(property)
    end

    def currently_available_at_property(property)
      presets =
        currently_available_in_tenant_portal
        .where(configuration: property.configuration)
        .select do |preset|
          next false if preset.is_a?(PaymentPlan::Preset::Explicit) && !preset.available?

          preset.property_whitelist.empty? || preset.property_whitelist.include?(property)
        end.reject do |preset|
          preset.property_blacklist.include?(property)
        end

      where(id: presets.pluck(:id))
    end
  end
end
