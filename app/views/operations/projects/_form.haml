= form_for [:operations, project], html: { class: 'ui project form' } do |f|
  - project_boards = Project::Board.order(name: :asc)
  - member_options = PropertyManager.where.not(id: current_property_manager.id)

  - if project_boards.one?
    = f.hidden_field :board_id, value: project_boards.first.id
  - else
    .required.field
      = f.label :board_id, 'Project Board'
      = f.semantic_dropdown :board_id,
        [['Select Board', nil]] + project_boards.pluck(:name, :id),
        { selected: params[:project_board_id] },
        data: { options: { placeholder: 'Select Board' } }

  %input#project-template-id{ type: 'hidden', name: 'project[template_id]' }

  .required.field
    = f.label :name
    = f.text_field :name,
      placeholder: 'e.g. Replace roof on Fox Groves Building 2'

  .field
    = f.label :description
    = f.text_area :description, rows: 4,
      placeholder: 'A brief overview of the project and its objectives'

  .field
    = f.label :property
    = f.semantic_dropdown :property_id,
      current_property_manager.properties.pluck(:name, :id),
      { include_blank: 'None' },
      class: 'ui search selection dropdown',
      data: { options: { placeholder: 'None' } }

  .field
    .ui.checkbox
      = f.check_box :public
      = f.label :public, 'Visible to All Users'

  .ui.very.basic.vertical.segment{ style: 'padding-top: 0; z-index: 0;' }
    .ui.inverted.dimmer#team-members-dimmer

    .field
      %label Team Members
      = f.semantic_dropdown :member_ids,
        member_options.map { |p| [p.name, p.id] },
        { select: f.object.project_memberships.map(&:user_id) },
        multiple: true,
        data: { options: { multiple: true, forceSelection: false } },
        class: 'ui multiple search selection dropdown'

    - if f.object.new_record?
      .ui.mini.message
        You can invite more team members later if you need to.

  - if f.object.new_record?
    .clearfix
      = f.submit class: 'right floated ui primary submit button'
  - else
    = f.submit class: 'ui primary submit button'

:javascript
  var publicCheckbox = $('#project_public').closest('.ui.checkbox');

  var dimmer = $('#team-members-dimmer')
  dimmer.dimmer({ duration: { show: 200, hide: 200 } })

  var setDimmer = function () {
    var action;

    if (publicCheckbox.checkbox('is checked')) {
      action = 'show';
    } else {
      action = 'hide';
    }

    dimmer.dimmer(action);
  };

  publicCheckbox.checkbox({ onChange: setDimmer });

  setDimmer();

  $('.ui.project.form').form({
    fields: {
      name: {
        identifier: 'project[name]',
        rules: [
          {
            type: 'maxLength[60]',
            prompt: 'Name cannot be longer than 60 characters',
          },
          {
            type: 'minLength[1]',
            prompt: 'Name must be present',
          }
        ],
      },
      description: {
        identifier: 'project[description]',
        rules: [
          {
            type: 'maxLength[250]',
            prompt: 'Description cannot be longer than 250 characters',
          },
        ],
      },
    }
  });
