class Leasing::BookingsController < LeasingController
  def index
    respond_to do |format|
      format.html { @noport = true }
      format.json { index_json }
    end
  end

  def index_json
    sorted = units.unarchived
                  .joins(:property)
                  .reorder(nil)
                  .order(PropertiesQuery::BROWNSORT)
                  .order(UnitsQuery::BROWNSORT)
                  .includes(:property, :leases)

    render json: sorted, each_serializer: BookingsTableSerializer
  end

  def amenities
    render json: Listing.all.flat_map(&:amenities).uniq.sort
  end

  private

  def grouped_units
    if params[:portfolio]
      type, id = params[:portfolio].split('-')
      if type == 'property'
        PropertiesQuery.for_user(current_property_manager).find(id).units
      else
        PortfoliosQuery.for_user(current_property_manager).find(id).units
      end
    else
      UnitsQuery.for_user(current_property_manager)
    end
  end

  def units
    if params[:amenities]
      UnitsQuery.new(grouped_units).search.filter_amenities(params[:amenities])
    else
      grouped_units
    end
  end
end
