= render Dashboards::CardComponent.new slug: :applications,
  title: 'Applications' do |c|

  - c.with_subtitle do
    Last 30 Days

  - c.with_right_link do
    = link_to 'View', leasing_pipeline_applications_path

  .ui.two.column.padded.divided.tiny.statistics.grid{ style: 'margin: auto !important;' }
    .ui.statistic.column
      .value
        = count
      .label
        Submitted

    .ui.statistic.column
      .value
        = revenue.format
      .label
        Revenue
