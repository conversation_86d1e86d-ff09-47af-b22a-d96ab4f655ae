class Tunisia::DebitCard::Create < Tunisia::Service
  include Tunisia::PhoneField

  ENDPOINT = '/cards'.freeze
  ACCOUNT_TYPE = 'depositAccount'.freeze
  DEFAULT_DESIGN = 'logo_debit'.freeze

  def initialize(form:, account_id:)
    super()
    @form = form
    @account_id = account_id
  end

  def call
    post(ENDPOINT, request)

    OpenStruct.new(successful?: true)
  end

  private

  attr_reader :form, :account_id

  def request
    {
      data: {
        type: type,
        attributes: data_attributes,
        relationships: {
          account: {
            data: {
              type: ACCOUNT_TYPE,
              id: account_id
            }
          }
        }
      }
    }
  end

  def type
    card_type = form.physical? ? :physical : :virtual

    Tunisia::DebitCardTypes::TO_TUNISIA_DEBIT_CARD_TYPES[card_type]
  end

  def data_attributes
    if form.physical?
      physical_card_attributes
    else
      virtual_card_attributes
    end
  end

  def shared_card_attributes
    {
      fullName: full_name,
      address: address_field(form.address),
      dateOfBirth: form.date_of_birth,
      email: form.email,
      phone: tunisia_formatted_phone(form.phone),
      limits: limits,
      idempotencyKey: form.idempotency_key
    }.merge(user_identifier)
  end

  def design_token
    CustomerSpecific::Behavior.tunisia_debit_card_design || DEFAULT_DESIGN
  end

  def physical_card_attributes
    shared_card_attributes.merge(
      {
        shippingAddress: address_field(form.shipping_address),
        design: design_token
      }
    )
  end

  def virtual_card_attributes
    shared_card_attributes
  end

  def address_field(address)
    {
      street: address.line_one,
      street2: address.line_two,
      city: address.city,
      state: address.region_code,
      postalCode: address.postal_code,
      country: 'US'
    }
  end

  def user_identifier
    if form.ssn.nil?
      passport
    else
      ssn
    end
  end

  def ssn
    { ssn: form.ssn.scan(/\d/).join }
  end

  def passport
    {
      passport: form.passport,
      nationality: form.nationality
    }
  end

  def full_name
    {
      first: form.first_name,
      last: form.last_name
    }
  end

  def limits
    {
      dailyWithdrawal: form.daily_withdrawal,
      dailyPurchase: form.daily_purchase,
      monthlyWithdrawal: form.monthly_withdrawal,
      monthlyPurchase: form.monthly_purchase
    }.transform_values { |money_str| parse_money(money_str) }
      .compact
  end

  def parse_money(money)
    cents = Monetize.parse(money).cents
    cents.positive? ? cents : nil
  end
end
