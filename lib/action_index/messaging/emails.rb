class ActionIndex::Messaging::Emails < ActionIndex::Base
  include ActionIndex::Shared::ContactFiltering

  def base_path
    messaging_emails_path
  end

  def filter_items
    [filter_contact, filter_read]
  end

  def selection_items
    [mark_read, mark_unread]
  end

  # rubocop:disable Style/WordArray
  def filter_read
    {
      type: 'dropdown',
      name: 'status',
      default_text: 'Everything',
      values: [
        ['Everything', ''],
        ['Read', 'read'],
        ['Unread', 'unread']
      ]
    }
  end
  # rubocop:enable Style/WordArray

  def mark_read
    {
      type: 'button',
      text: 'Mark Read',
      action: 'read'
    }
  end

  def mark_unread
    {
      type: 'button',
      text: 'Mark Unread',
      action: 'unread'
    }
  end
end
