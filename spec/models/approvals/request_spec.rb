require 'rails_helper'

RSpec.describe Approvals::Request do
  describe 'the factory' do
    subject { build(:approvals_request) }

    it { is_expected.to be_valid }

    context 'approved' do
      subject { build(:approvals_request, :approved) }

      it { is_expected.to be_valid }
    end
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:approvable) }

    it { is_expected.to validate_presence_of(:action) }

    it { is_expected.to validate_presence_of(:requested_by) }

    it { is_expected.to validate_presence_of(:approver) }
  end

  describe 'scopes' do
    it 'has a pending scope' do
      request = create(:approvals_request)
      expect(described_class.pending).to include(request)

      request = create(:approvals_request, :approved)
      expect(described_class.pending).not_to include(request)

      request = create(:approvals_request, :rejected)
      expect(described_class.pending).not_to include(request)
    end
  end
end
