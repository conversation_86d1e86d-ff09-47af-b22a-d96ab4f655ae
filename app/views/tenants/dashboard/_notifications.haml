- inbox = PortalMessaging::Inbox.new(user: current_tenant)

.notifications
  .ui.container
    .ui.two.column.grid
      .column
        %h1.ui.header
          Notifications
      .right.aligned.column
        .notification-count
          %i.certificate.icon
          = pluralize(inbox.unread_count, 'Notification')

    = render partial: 'portal_messaging/table',
      locals: { messages: inbox.recent_unread_messages }
