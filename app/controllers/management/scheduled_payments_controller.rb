class Management::ScheduledPaymentsController < ManagementController
  include ::ScheduledPaymentsController
  include ::PaymentMethodsController

  def new
    super

    return unless @parent.is_a?(Tenant)

    @scheduled_payment = ScheduledPayment.new(
      description: Customer.current.greek_housing? ? 'Scheduled Payment' : 'Monthly Rent',
      amount: lease_membership&.amount,
      date: Time.zone.today.next_month.beginning_of_month,
      recurring: true
    )

    if current_lease_membership
      @scheduled_payment.lease_membership = current_lease_membership
    elsif current_simple_agreement
      @scheduled_payment.simple_agreement = current_simple_agreement
    end
  end

  def available_merchant_accounts
    Tenant::MerchantAccountsQuery.available_merchant_accounts(tenant: @parent)
  end

  def available_payment_methods
    @parent
      .payment_methods
      .select { |pm| pm.merchant_account.in?(available_merchant_accounts) }
  end

  def show
    @audits = @scheduled_payment.audits
  end

  helper_method :available_payment_methods

  private

  def lease_membership
    @parent.current_lease_membership || @parent.lease_memberships.last
  end

  def current_lease_membership
    return nil unless @parent.is_a?(Tenant)

    Tenant::LeaseMembershipsQuery.current_lease_membership(tenant: @parent)
  end

  def current_simple_agreement
    return nil unless @parent.is_a?(Tenant)

    Tenant::SimpleAgreementsQuery.current_simple_agreement(tenant: @parent)
  end
end
