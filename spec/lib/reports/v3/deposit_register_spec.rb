require 'rails_helper'

require_relative 'shared'

RSpec.describe Reports::V3::DepositRegister do
  subject(:report) { described_class.new(user: user) }

  let(:user) { create(:property_manager, top_level: true) }

  before do
    bank_account = create(:bank_account)
    payment = create(:payment,
                     payer: bank_account.owner,
                     kind: :check,
                     check_number: 1234,
                     description: 'Sample Payment',
                     amount: '$100.00',
                     credit_bank_account: bank_account)
    payment.payer.update!(name: 'Sample Company')
    payment.payee.update!(name: 'Sample Vendor')
  end

  it_behaves_like 'a v3 report'
end
