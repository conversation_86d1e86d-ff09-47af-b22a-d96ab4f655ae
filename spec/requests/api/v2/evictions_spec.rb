require 'swagger_helper'

require_relative 'rswag_context'

RSpec.describe 'api/v2/evictions' do
  include_context 'rswag api v2'

  let!(:eviction) do
    create(:collections_eviction).tap do |eviction|
      api_user.property_memberships.create!(target: eviction.lease.property)
    end
  end

  path '/api/v2/evictions' do
    get('list evictions') do
      response(200, 'successful') do
        run_test!
      end
    end
  end

  path '/api/v2/evictions/{id}' do
    parameter name: :id, in: :path, type: :integer, description: 'id'

    get('show eviction') do
      response(200, 'successful') do
        let(:id) { eviction.id }

        run_test!
      end
    end
  end
end
