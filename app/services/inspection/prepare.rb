class Inspection::Prepare
  extend Service

  attr_reader :user, :params, :target

  def initialize(user:, params:)
    @user = user
    @params = params
    @target = locate_target
  end

  def call
    fail InspectionTargetError, 'Inspection must have an inspection area' if target.nil?

    inspection = Inspection::Report.new(inspection_report_params)

    inspection.opened_by = user
    inspection.assigned_to = assigned_to
    inspection.target = target
    inspection.property = if target.is_a?(Property)
                            target
                          else
                            target.property
                          end

    inspection.validate!

    add_records(inspection, target)

    if inspection.records.none?
      fail InspectionTargetError, 'Inspection must have at least one record'
    end

    yield inspection if block_given?

    inspection.save!

    OpenStruct.new(successful?: true, inspection: inspection)
  rescue ActiveRecord::RecordInvalid => e
    # The property validation is the exact same as the target validation effectively.
    # This is being deleted to hide an error that makes no sense on the form.
    e.record.errors.delete(:property)
    OpenStruct.new(successful?: false, errors: e.record.errors.full_messages)
  rescue InspectionTargetError => e
    OpenStruct.new(successful?: false, errors: [e.message])
  end

  private

  class InspectionTargetError < StandardError; end

  def inspection_report_params
    params.require(:inspection_report).permit(
      :kind, :name, :template_id, :earliest_start_date, :due_date, :entry_code, :message
    )
  end

  def locate_target
    GlobalID::Locator.locate_signed(params.dig(:inspection_report, :target_id))
  end

  def assigned_to
    GlobalID::Locator.locate_signed(
      params.dig(:inspection_report, :assigned_to)
    )
  end

  def add_records(inspection, target)
    template = inspection.template

    case target
    when Unit
      inspection.records.build(target: target)
    when Property
      if target.single_unit_type?
        inspection.records.build(target: target.units.last)
      else
        if template.references_property?
          inspection.records.build(target: target)
        end

        if template.references_units?
          target.units.unarchived.each do |unit|
            inspection.records.build(target: unit)
          end
        end
      end
    else
      fail InspectionTargetError, 'Unsupported inspection target'
    end
  end
end
