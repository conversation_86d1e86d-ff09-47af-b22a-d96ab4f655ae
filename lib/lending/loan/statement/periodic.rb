class Lending::Loan::Statement::Periodic < Lending::Loan::Statement
  def initialize(loan:, date: Time.zone.today)
    super(loan: loan)
    @date = date
  end

  def entries
    @entries ||= load_entries
  end

  def activity_date_range
    activity_start_date..activity_end_date
  end

  def activity_start_date
    (activity_end_date - 45.days).beginning_of_month
  end

  def activity_end_date
    @date
  end

  def description
    "#{@date.strftime('%B')} Statement"
  end

  private

  def load_entries
    if (entries = loan.load_serialized_entries)
      entries.select { |entry| entry.date.in?(activity_date_range) }
    else
      Lending::Loan::Statement::Entry.for_loan_by_range(
        loan: loan,
        date_range: activity_date_range
      )
    end
  end
end
