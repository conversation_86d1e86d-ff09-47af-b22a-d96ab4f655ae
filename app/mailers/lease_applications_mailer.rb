class LeaseApplicationsMailer < ApplicationMailer
  include <PERSON><PERSON><PERSON><PERSON><PERSON>

  def send_application_invite(lease_application)
    @email = lease_application.email
    @property = lease_application.property
    @expires_at = lease_application.expires_at
    @uuid = lease_application.uuid
    mail to: @email, subject: subject
  end

  def send_application_thankyou(lease_application)
    email = lease_application.email.presence
    @property = lease_application.property
    @expires_at = lease_application.expires_at
    @uuid = lease_application.uuid
    mail to: email, subject: subject if email
  end

  def send_completed_application(lease_application, pdf)
    @property = lease_application.property
    emails = lease_application.tenants.pluck(:email).compact

    filename = 'rental_application.pdf'
    attachments[filename] = pdf
    mail to: emails, subject: 'Rental Application Submitted' if emails.any?
  end

  def send_expiration_reminder(lease_application)
    @lease_application = lease_application

    email = lease_application.email

    return if email.blank?

    mail to: email, subject: 'Lease Application Expiring Tomorrow'
  end

  def send_reminder(lease_application)
    @lease_application = lease_application
    @property = lease_application.property

    email = lease_application.email

    return if email.blank?

    mail to: email, subject: subject
  end

  def request_payment(lease_application)
    @uuid = lease_application.uuid
    @first_name = lease_application.primary_tenant.first_name
    @property = lease_application.property

    mail to: lease_application.email,
         subject: 'Submit Lease Application Payment'
  end

  private

  def subject
    "Lease Application to #{@property.name}"
  end
end
