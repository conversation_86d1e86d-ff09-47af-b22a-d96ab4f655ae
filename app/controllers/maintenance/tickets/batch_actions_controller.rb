class Maintenance::Tickets::BatchActionsController < MaintenanceController
  before_action :set_tickets

  def archive
    # TODO
    flash_and_redirect!(0, 'Archived')
  end

  def assign
    user = PropertyManager.find_by(id: params[:assignee_id]) # Nil for unassign

    count = 0

    ActiveRecord::Base.transaction do
      @maintenance_tickets.each do |ticket|
        if ticket.assigned_user != user
          count += 1
          ticket.update!(assigned_user: user)
        end
      end
    end

    action = if params[:assignee_id] == '0'
               'Unassigned'
             else
               'Assigned'
             end

    flash_and_redirect!(count, action)
  end

  def tag
    tag = Tag.find(params[:tag_id])

    count = 0

    ActiveRecord::Base.transaction do
      @maintenance_tickets.each do |ticket|
        unless ticket.tags.include?(tag)
          count += 1
          ticket.tags << tag
        end
      end
    end

    flash_and_redirect!(count, 'Tagged')
  end

  def reopen
    count = ActiveRecord::Base.transaction do
      @maintenance_tickets.select(&:may_reopen?).count(&:reopen!)
    end

    flash_and_redirect!(count, 'Reopened')
  end

  def resolve
    count = ActiveRecord::Base.transaction do
      @maintenance_tickets.select(&:may_resolve?).count(&:resolve!)
    end

    flash_and_redirect!(count, 'Resolved')
  end

  def close
    count = ActiveRecord::Base.transaction do
      @maintenance_tickets.select(&:may_close?).count(&:close!)
    end

    flash_and_redirect!(count, 'Closed')
  end

  def close
    count = ActiveRecord::Base.transaction do
      @maintenance_tickets.select(&:may_close?).count(&:close!)
    end

    flash_and_redirect!(count, 'Closed')
  end

  def urgency
    urgency = params[:urgency]

    count = 0

    ActiveRecord::Base.transaction do
      @maintenance_tickets.each do |ticket|
        if ticket.urgency != urgency
          count += 1
          ticket.update!(urgency: urgency)
        end
      end
    end

    flash_and_redirect!(count, 'Updated')
  end

  private

  def set_tickets
    @maintenance_tickets = MaintenanceTicket.where \
      id: params[:selected_ids]
  end

  def flash_and_redirect!(count, action)
    flash[:success] = [
      count,
      'Ticket'.pluralize(count),
      action
    ].join(' ')

    redirect_back(fallback_location: maintenance_tickets_path)
  end
end
