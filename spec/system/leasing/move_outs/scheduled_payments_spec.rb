require 'rails_helper'

RSpec.describe 'cancelling scheduled payments during move out', :js,
               capybara_login: :property_manager do
  let(:move_out) { create(:lease_move_out) }

  let!(:scheduled_payment) do
    create(:scheduled_payment,
           lease_membership: move_out.lease.lease_memberships.first)
  end

  scenario 'a user cancells upcoming scheduled payments' do
    visit review_leasing_lease_move_out_path(move_out.lease, move_out)

    expect(page).to have_content(scheduled_payment.amount.format)

    check_checkbox 'Cancel Upcoming Scheduled Payments'

    expect do
      click_on 'Submit'

      expect(page).to have_content(/successful/i)
    end.to change { ScheduledPayment.count }.by(-1)
  end
end
