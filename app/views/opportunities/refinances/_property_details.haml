.ui.basic.vertical.segment
  .ui.info.message
    %p Property Details

  .three.required.fields
    .field
      = f.label :deed_type, 'How Is the Property Deeded?'
      = f.semantic_dropdown :deed_type,
        [['Select', nil],
        ['Business Name', 'business_deed'],
        ['Personal Name', 'personal_deed']],
        {},
        class: 'ui search selection dropdown'

    .field
      = f.label :deed_name, 'What Is the Name on the Deed?'
      = f.text_field :deed_name

    .field
      = f.label :purchase_date, 'When Was the Property Purchased?'
      = f.semantic_date_field :purchase_date

  .two.required.fields
    .field
      = f.label :lein_type, 'Is There a Lein on the Property?'
      = f.semantic_dropdown :lein_type,
        [['Select', nil],
        ['No Lein', 'no_lein'],
        ['Mortgage', 'mortgage'],
        ['Line of Credit', 'line_of_credit'],
        ['Tax Lein', 'tax_lein'],
        ['Other', 'other_lein']],
        {},
        class: 'ui search selection dropdown'

    .field
      = f.label :lein_interest_rate, 'What Is the Interest Rate?'
      = f.number_field :lein_interest_rate,
        placeholder: '0%',
        step: :any,
        min: 0
