class PaymentMethodsMailerPreview < ActionMailer::Preview
  def payment_method_added
    owner = [
      -> { Tenant.first || FactoryBot.build(:tenant) },
      -> { Company.first || FactoryBot.build(:company) },
      -> { Vendor.first || FactoryBot.build(:vendor) }
    ].sample.call

    type = [:credit_card, :bank_account].sample

    payment_method = FactoryBot.build(type, owner: owner)

    PaymentMethodsMailer.payment_method_added(payment_method)
  end
end
