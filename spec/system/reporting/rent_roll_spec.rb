require 'rails_helper'

RSpec.describe 'rent roll report', :js,
               capybara_login: :property_manager do
  let(:lease_amount) { Monetize.parse('$100.00') }

  let!(:tenant) do
    create(:resident).tap do |tenant|
      lease = tenant.current_lease

      configuration = lease.configuration

      chart_of_accounts = configuration.chart_of_accounts

      income = create(:revenue_account,
                      category: 'Rent Income',
                      tenant: chart_of_accounts)

      create(:charge_preset, configuration: configuration,
                             kind: :rent,
                             account: income)

      create(:charge_schedule_entry,
             lease: lease, name: 'Monthly Rent',
             amount: lease_amount, recurring: true,
             account: income,
             start_date: lease.start_date,
             end_date: lease.end_date)

      lease.unit.update!(created_at: 1.year.ago)
    end
  end

  let(:membership) { tenant.lease_memberships.last }

  let!(:rent_invoice) { create(:rent_invoice, membership: membership) }

  before { visit '/reports/rent-roll' }

  describe 'report rows' do
    subject(:table) { find('tbody') }

    it 'contains the tenant name' do
      expect(table).to have_content(tenant.name)
    end

    it 'contains the unit' do
      expect(table).to have_content(tenant.current_unit.name)
    end

    it 'contains the rent amount' do
      expect(table).to have_content(lease_amount.format)
    end

    it 'contains the current balance' do
      expect(table).to have_content(membership.balance.format)
    end
  end
end
