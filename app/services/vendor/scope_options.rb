class Vendor::ScopeOptions
  attr_reader :vendor, :property_manager

  def self.enabled?
    enabled_for_properties? || enabled_for_portfolios?
  end

  def self.enabled_for_properties?
    Feature.enabled?(:scope_vendors_to_properties, Customer.current)
  end

  def self.enabled_for_portfolios?
    Feature.enabled?(:scope_vendors_to_portfolios, Customer.current)
  end

  def initialize(vendor, property_manager)
    @vendor = vendor
    @property_manager = property_manager
  end

  def selected_properties
    @selected_properties ||= vendor.entity_vendor_scopes.includes(:entity)
                                   .where(entity_type: 'Property').map(&:entity)
  end

  def selected_property_ids
    @selected_property_ids ||= selected_properties.pluck(:id).to_set
  end

  def own_properties
    @own_properties ||= PropertiesQuery.new.search.by_user(property_manager)
  end

  def own_property_ids
    @own_properties_ids ||= own_properties.pluck(:id).to_set
  end

  def selectable_properties
    @selectable_properties ||= own_properties.unarchived
  end

  def selectable_property_ids
    @selectable_property_ids ||= selectable_properties.pluck(:id).to_set
  end

  def selected_portfolios
    @selected_portfolios ||= vendor.entity_vendor_scopes.includes(:entity)
                                   .where(entity_type: 'Portfolio').map(&:entity)
  end

  def selected_portfolio_ids
    @selected_portfolio_ids ||= selected_portfolios.pluck(:id).to_set
  end

  def selectable_portfolios
    @selectable_portfolios ||= PortfoliosQuery.new.search.by_user(property_manager)
  end

  def selectable_portfolio_ids
    @selectable_portfolio_ids ||= selectable_portfolios.pluck(:id).to_set
  end

  def deselectable_property_ids
    @deselectable_property_ids ||= selected_property_ids & own_property_ids
  end

  def property_ids_disabled?(property_ids)
    !selectable_property_ids.intersect?(property_ids.to_set) &&
      !deselectable_property_ids.intersect?(property_ids.to_set)
  end

  def property_dropdown_options
    return [] unless self.class.enabled_for_properties?

    (selected_properties.to_a + selectable_properties.to_a).uniq(&:id).map do |property|
      name = property.archived? ? "#{property.name} (Archived)" : property.name
      { text: name, name: name, icon: 'home',
        value: property.to_gid.to_s,
        disabled: property_ids_disabled?([property.id]),
        selected: selected_property_ids.include?(property.id) }
    end
  end

  def portfolio_dropdown_options
    return [] unless self.class.enabled_for_portfolios?

    (selected_portfolios.to_a + selectable_portfolios.to_a).uniq(&:id).map do |portfolio|
      { text: portfolio.name, name: portfolio.name, icon: 'briefcase',
        value: portfolio.to_gid.to_s, disabled: selectable_portfolio_ids.exclude?(portfolio.id),
        selected: selected_portfolio_ids.include?(portfolio.id) }
    end
  end

  def dropdown_options
    return @dropdown_options if defined?(@dropdown_options)

    @dropdown_options = property_dropdown_options + portfolio_dropdown_options
  end

  def locked_gids
    dropdown_options.select { |o| o[:disabled] }.pluck(:value)
  end

  def all_editable?(scope_attrs)
    return true if scope_attrs.blank?

    property_ids = scope_attrs.select do |p|
      p[:entity_type] == 'Property'
    end.to_set { |p| p[:entity_id] }
    portfolio_ids = scope_attrs.select do |p|
      p[:entity_type].include?('Portfolio')
    end.to_set { |p| p[:entity_id] }

    !property_ids_disabled?(property_ids.map(&:to_i)) &&
      portfolio_ids <= selectable_portfolio_ids
  end

  class GidsToAttributes
    attr_reader :vendor, :gids

    def initialize(vendor, scoped_to_gids_str)
      @vendor = vendor
      @gids = (scoped_to_gids_str || '')&.try!(:split, ',') || []
      @to_destroy = initial_to_destroy
    end

    def initial_to_destroy
      vendor.entity_vendor_scopes.map do |esp|
        { id: esp.id, vendor_id: esp.vendor_id, entity_id: esp.entity_id,
          entity_type: esp.entity_type, _destroy: true }
      end
    end

    def convert
      to_add = gids.filter_map do |gid|
        located = GlobalID::Locator.locate(gid)
        next unless located

        existing_found = @to_destroy.find do |td|
          td[:entity_id] == located.id && td[:entity_type] == located.class.name
        end

        if existing_found
          @to_destroy.delete(existing_found)
          next
        else
          { entity_id: located.id, entity_type: located.class.name }
        end
      end

      @to_destroy + to_add
    end
  end

  def entity_scope_vendor_attributes(scoped_to_gids_str)
    GidsToAttributes.new(vendor, scoped_to_gids_str).convert
  end
end
