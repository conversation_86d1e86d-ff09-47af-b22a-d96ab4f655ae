module HasAddress
  extend ActiveSupport::Concern

  def reject_if_blank_line_one(attributes)
    attributes['line_one'].blank?
  end

  included do
    has_one :address, -> { where(kind: :default) },
            as: :addressable,
            inverse_of: :addressable,
            dependent: :destroy

    has_one :mailing_address, -> { where(kind: :mailing) },
            class_name: 'Address',
            as: :addressable,
            inverse_of: :addressable,
            dependent: :destroy,
            required: false

    accepts_nested_attributes_for :address,
                                  reject_if: :reject_if_blank_line_one

    accepts_nested_attributes_for :mailing_address,
                                  reject_if: :reject_if_blank_line_one,
                                  allow_destroy: true
  end

  def address_for_mailed_checks
    mailing_address || address
  end
end
