require 'rails_helper'

RSpec.describe CustomForms::Element::Dropdown do
  describe 'the factory' do
    subject { create(:custom_forms_element_dropdown) }

    it { is_expected.to be_valid }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:label) }

    it 'does not allow a dropdown without options to be saved' do
      dropdown = create(:custom_forms_element_dropdown)
      dropdown.options.destroy_all
      expect(dropdown.save).to be false
      expect(dropdown.errors.messages[:base].first).to eq 'There must be at least one choice'
    end
  end

  describe 'validate_submitted_response' do
    subject(:result) { dropdown.validate_submitted_response(value) }

    let(:dropdown) { create(:custom_forms_element_dropdown, required: required) }

    context 'when it is required' do
      let(:required) { true }

      context 'when an empty label string is received' do
        let(:value) { '' }

        it 'returns a message' do
          expect(result).to eq "#{dropdown.label.capitalize} is required"
        end
      end

      context 'when an invalid option string is received' do
        let(:value) { SecureRandom.uuid }

        it 'returns a message' do
          expect(result).to eq "#{value} is not valid for #{dropdown.label.capitalize}"
        end
      end

      context 'when a valid option is received' do
        let(:value) { dropdown.options.first.value }

        it 'returns nil' do
          expect(result).to be_nil
        end
      end
    end

    context 'when it is not required' do
      let(:required) { false }

      context 'when an invalid label string is received' do
        let(:value) { SecureRandom.uuid }

        it 'returns a message' do
          expect(result).to eq "#{value} is not valid for #{dropdown.label.capitalize}"
        end
      end

      context 'when a valid option is received' do
        let(:value) { dropdown.options.first.value }

        it 'returns nil' do
          expect(result).to be_nil
        end
      end
    end
  end
end
