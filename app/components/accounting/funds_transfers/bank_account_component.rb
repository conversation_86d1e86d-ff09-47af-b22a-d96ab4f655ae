class Accounting::FundsTransfers::BankAccountComponent < ApplicationComponent
  def initialize(bank_account:)
    super()
    @bank_account = bank_account
  end

  private

  attr_reader :bank_account

  def link
    new_accounting_journal_funds_transfer_path(
      bank_account.owner, bank_account_id: bank_account.id
    )
  end

  def name
    bank_account.name
  end

  def last_four
    helpers.last_n bank_account.account_number, 4
  end

  def review_description
    bank_account.decorate.last_funds_transfer_review_description
  end
end
