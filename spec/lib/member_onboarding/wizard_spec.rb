require 'rails_helper'

RSpec.describe MemberOnboarding::Wizard do
  let(:token) { SecureRandom.hex(10) }
  let(:wizard) { described_class.new(token: token) }

  describe '#set_properties_filtered_flag and #properties_filtered?' do
    it 'sets and retrieves the properties filtered flag' do
      expect(wizard.properties_filtered?).to be false

      wizard.set_properties_filtered_flag

      expect(wizard.properties_filtered?).to be true
    end

    it 'persists the flag in Redis across wizard instances' do
      wizard.set_properties_filtered_flag

      # Create a new wizard instance with the same token
      new_wizard = described_class.new(token: token)

      expect(new_wizard.properties_filtered?).to be true
    end

    it 'returns false when no flag is set' do
      expect(wizard.properties_filtered?).to be false
    end
  end
end

