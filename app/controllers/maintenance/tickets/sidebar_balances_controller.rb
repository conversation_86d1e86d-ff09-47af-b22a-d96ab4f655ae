##
# This controller is used for lazily loading the associated balances of the
# tenant or managed owner entity in the work order sidebar
class Maintenance::Tickets::SidebarBalancesController < MaintenanceController
  before_action :set_maintenance_ticket,
                :set_tenant_balance,
                :set_entity_balance

  def show; end

  private

  def set_maintenance_ticket
    @maintenance_ticket = \
      MaintenanceTicketsQuery
      .new.search
      .by_user(current_property_manager)
      .find(params[:ticket_id])
  end

  def set_tenant_balance
    @tenant_balance = @maintenance_ticket.associated_tenant_balance
  end

  def set_entity_balance
    @entity_balance = @maintenance_ticket.associated_entity_balance
  end
end
