class Api::Zapier::BaseController < ActionController::API
  include <PERSON><PERSON><PERSON><PERSON>

  before_action only: [:create, :update, :destroy] do
    doorkeeper_authorize! :write
  end

  before_action only: [:index, :show] do
    doorkeeper_authorize! :read
  end

  before_action :set_current_property_manager

  private

  attr_reader :current_property_manager

  def set_current_property_manager
    if doorkeeper_token.present?
      @current_property_manager = PropertyManager.find_by(id: doorkeeper_token.resource_owner_id)
      head :unauthorized unless @current_property_manager
    else
      head :unauthorized
    end
  end

  def can(action)
    return if current_property_manager.can.send(action)

    render json: { error: "#{action} unauthorized" }, status: :unauthorized
  end
end
