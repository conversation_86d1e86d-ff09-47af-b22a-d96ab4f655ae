- if @credit_presets.none?
  .ui.info.message
    .header Credit Presents
    %p
      You can configure credit presets here that will be available for users to give #{I18n.t('tenant_term').downcase.pluralize}.
      Click
      %i Add Credit Preset
      to get started.

- else
  = render partial: 'organization/configurations/credit_presets/table',
    locals: { credit_presets: @credit_presets }

= link_to 'Add Credit Preset',
  [:new, *configuration_path, :credit_preset],
  class: 'ui basic button'

.clearfix
  :ruby
    next_sym = if Feature.enabled?(:payment_plans, Customer.current)
                 :payment_plan_presets
               else
                 :applications
               end

    link = if @portfolio
             [next_sym, *configuration_path]
           else
             [*configuration_path, next_sym]
           end

  = link_to 'Next', link, class: 'right floated ui button'
