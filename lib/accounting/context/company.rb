class Accounting::Context::Company < Accounting::Context::Base
  attr_reader :company

  def initialize(company)
    @company = company
  end

  delegate :name, to: :company
  delegate :accounts, :chart_of_accounts, to: :company

  alias entity company
  alias journal company

  def entry_params
    {
      journal_id: journal.id,
      property_id: single_property&.id,
      unit_id: single_unit&.id
    }.compact
  end

  # Prevent overspecifying past journal entries only applied to the company id,
  # even though generated entries should still provide property and unit id for
  # single targets when applicable.
  def query_params
    { journal_id: journal.id }
  end

  def as_json(options = {})
    {
      id: company.to_sgid.to_s,
      name: company.name,
      type: 'Entity',
      child: child_type,
      chart_of_accounts_id: chart_of_accounts.id
    }
  end

  def children
    return company.properties if company.properties.many?
    return single_property.units if single_property&.units&.many?
  end

  def child_type
    return 'Property' if properties.many?
    return 'Unit' if single_property&.units&.many?
  end

  private

  delegate :properties, to: :company

  def single_property
    properties.first if properties.one?
  end

  def single_unit
    single_property.units.first if single_property&.units&.one?
  end
end
