.ui.breadcrumb
  = link_to 'Bank Accounts',
    organization_bank_accounts_path,
    class: 'section'
  %i.right.angle.icon.divider

  = link_to @bank_account.name,
    organization_bank_account_path(@bank_account),
    class: 'section'
  %i.right.angle.divider.icon

.action-bar
  .ui.container
    %h1.ui.header
      Transactions in Review (#{@transactions_count})

    .action.buttons
      = link_to "Post to Ledger (#{@reviewed_transactions_count})", '#',
          data: { modal: :post_transactions_modal },
          class: 'ui primary button'

= ActionIndex::Accounting::DebitCardPurchases.(self,
  collection: @filtered_debit_card_purchases,
  user: current_property_manager,
  partial: { path: 'organization/debit_card_purchases/table',
  locals: { debit_card_purchases: @filtered_debit_card_purchases } })

%div{ style: 'padding-top: 1rem;' }
  = paginate @filtered_debit_card_purchases

= render 'bulk_update_transactions_modal'
= render 'ignore_transactions_modal'

.ui.container
  = render Accounting::BankAccounts::PostTransactionsModalComponent.new count: @reviewed_transactions_count,
   bank_account: @bank_account
