.ui.container
  %h1.ui.header
    Make a Contribution

  - if @payment_method
    = render partial: 'shared/payment_processing/checkout_page',
      locals: { payment_methods: available_payment_methods,
      payment_method: @payment_method,
      path: submit_owners_contribution_path(@owner_contribution),
      email: current_owner.email,
      cart: @cart }
  - else
    = render Owners::ElectronicPayments::NoPaymentMethodComponent.new(entity: @company)
