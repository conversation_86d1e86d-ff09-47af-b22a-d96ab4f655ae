require 'rails_helper'

require_relative 'shared'

RSpec.describe Reports::V3::ScheduledPayments do
  subject(:report) { described_class.new(user: user) }

  let(:user) { create(:property_manager, top_level: true) }

  before do
    scheduled_payments = create_pair(:scheduled_payment,
                                     recurring: false,
                                     pay_balance: false,
                                     amount: '$100',
                                     date: Date.new(2021, 1, 1))

    scheduled_payments.each do |scheduled_payment|
      scheduled_payment.source.update!(
        first_name: '<PERSON><PERSON>', last_name: 'Tenant'
      )

      scheduled_payment.property.update!(
        name: 'Sample Property'
      )
    end

    scheduled_payments.first.failed!
  end

  it_behaves_like 'a v3 report'
end
