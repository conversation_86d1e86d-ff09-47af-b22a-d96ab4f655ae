require 'rails_helper'

RSpec.describe 'leasing waitlist pipeline', :js,
               capybara_login: :property_manager do
  # Two different properties
  let(:properties) { create_pair(:property) }

  # Three floorplans, two at the first property
  let(:floorplans) do
    [
      create(:floorplan, property: properties.first, name: 'P1F1'),
      create(:floorplan, property: properties.first, name: 'P1F2'),
      create(:floorplan, property: properties.last, name: 'P2F1')
    ]
  end

  # Three applicants
  let(:cartman) { create(:tenant) }
  let(:kenny) { create(:tenant) }
  let(:kyle) { create(:tenant) }

  # Four waitlist entries
  let!(:waitlist_entries) do
    p1f1, p1f2, p2f1 = floorplans

    [
      create(:waitlist_entry, applicant: cartman, floorplan: p1f1),
      create(:waitlist_entry, applicant: cartman, floorplan: p1f2),
      create(:waitlist_entry, applicant: kenny, floorplan: p1f1),
      create(:waitlist_entry, applicant: kyle, floorplan: p2f1)
    ]
  end

  let(:position) { 3 }

  before do
    allow_any_instance_of(WaitlistEntry).to \
      receive(:position) { position }

    allow_any_instance_of(WaitlistEntry).to \
      receive(:expected_date) { 5.days.from_now }

    visit leasing_leads_path

    expect(page).to have_content(floorplans.first.name)
  end

  xdescribe 'filtering' do
    def filter(name)
      select_dropdown 'waitlist_filter', name
    end

    scenario 'a user filters by property' do
      p1_applicants = [cartman, kenny]
      p2_applicants = [kyle]

      filter(properties.first.name)
      p1_applicants.each { |a| expect(page).to have_content(a.name) }
      p2_applicants.each { |a| expect(page).to have_no_content(a.name) }

      filter(properties.last.name)
      p1_applicants.each { |a| expect(page).to have_no_content(a.name) }
      p2_applicants.each { |a| expect(page).to have_content(a.name) }
    end

    scenario 'floorplan' do
      filter('P2F1')
      expect(page).to have_content(kyle.name)
      expect(page).to have_no_content(cartman.name)
      expect(page).to have_no_content(kenny.name)
    end
  end

  describe 'looking at a waitlist entry' do
    subject(:card) { find_all('.waitlist .card').last }

    let(:entry) { waitlist_entries.last }

    it 'has the tenant name' do
      expect(card).to have_content(entry.applicant.name)
    end

    it 'has the floorplan' do
      expect(card).to have_content(entry.floorplan.name)
    end

    it 'has the waitlisted time' do
      expect(card).to have_content('a few seconds ago')
    end

    context 'at the front of the list' do
      let(:position) { 0 }

      it 'is marked as ready' do
        expect(card).to have_content('Ready')
      end
    end

    context 'waiting' do
      it 'has the position' do
        expect(card).to have_content('#3')
      end

      it 'expected wait' do
        expect(card).to have_content('5 days')
      end
    end
  end
end
