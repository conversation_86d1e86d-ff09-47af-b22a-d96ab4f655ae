.ui.three.column.divided.stackable.tiny.statistics.grid
  .ui.statistic.column
    .value= contract.public_housing_authority
    .label Public Housing Authority

  .ui.statistic.column
    .value= contract.start_date.strftime('%B %-e, %Y')
    .label Contract Start Date

  .ui.statistic.column
    .value= contract.end_date.strftime('%B %-e, %Y')
    .label Contract End Date

%table.ui.definition.fixed.table
  %thead
    %tr
      %th
        %b Contract Unit Designation
      %th Required
      %th Actual
  %tbody
    %tr
      %td Any LIHTC Designation
      %td= contract.contract_unit_count
      %td= contract.property.units.count(:lihtc_designation)
    %tr
      %td Disabled
      %td= contract.disabled_unit_count
      %td= contract.property.units.lihtc_disabled.count
    %tr
      %td Elderly
      %td= contract.elderly_unit_count
      %td= contract.property.units.lihtc_elderly.count
    %tr
      %td Elderly or Disabled
      %td= contract.elderly_or_disabled_unit_count
      %td= contract.property.units.lihtc_elderly_or_disabled.count
    %tr
      %td Supportive Services
      %td= contract.supportive_services_unit_count
      %td= contract.property.units.lihtc_supportive_services.count
