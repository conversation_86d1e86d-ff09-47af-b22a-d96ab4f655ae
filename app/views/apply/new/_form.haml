.ui.bottom.attached.clearing.form.segment
  = form_with url: hosted_applications_path,
    local: true,
    html: { class: 'ui form' } do |f|

    = f.hidden_field :unit_id, value: @unit&.id

    = f.hidden_field :floorplan_id, value: @floorplan&.id

    = f.hidden_field :property_id, value: @property.id

    = f.hidden_field :lead_id, value: params[:lead_id]

    .field
      = f.label :email, 'Email Address'
      = f.email_field :email, value: params[:email]

    = recaptcha_tags

    %br

    = f.submit 'Begin Application', class: 'ui primary submit button'
