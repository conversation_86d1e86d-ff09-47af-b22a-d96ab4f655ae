class Property::MigrateEntity
  extend Service

  def initialize(property:, destination_company:)
    @property = property
    @destination_company = destination_company
  end

  def call
    ActiveRecord::Base.transaction do
      @property.update!(company: @destination_company)

      move_journal_entries
      bust_journal_caches
      bust_account_caches
    end

    OpenStruct.new(successful?: true, property: @property)
  rescue ActiveRecord::RecordInvalid => e
    OpenStruct.new(errors: e.record.errors.full_messages)
  end

  private

  # rubocop:disable Rails/SkipsModelValidations
  def move_journal_entries
    entries = Plutus::Entry.where(property: @property, journal: source_journal)

    entries.update_all(
      journal_id: destination_journal.id,
      updated_at: Time.zone.now
    )
  end

  def bust_journal_caches
    source_journal.touch(:accounting_updated_at)
    destination_journal.touch(:accounting_updated_at)
  end

  def bust_account_caches
    charts = [
      source_journal.chart_of_accounts,
      destination_journal.chart_of_accounts
    ]

    charts.uniq.each do |chart|
      chart.accounts.touch_all
    end
  end
  # rubocop:enable Rails/SkipsModelValidations

  def source_journal
    @property.company
  end

  def destination_journal
    @destination_company
  end
end
