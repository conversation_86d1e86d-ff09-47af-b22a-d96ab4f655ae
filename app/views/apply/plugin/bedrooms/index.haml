:ruby
  query = {
    property_id: params[:property_id],
    portfolio_id: params[:portfolio_id],
    embed: params[:embed]
  }.compact

%h1.ui.header
  Floor Plan Styles
  .sub.header
    = @scope.name

%table.ui.single.line.selectable.striped.sortable.fixed.table
  %thead
    %tr
      %th.center.aligned Style
      %th.center.aligned Square Feet
      %th.center.aligned Rent
      %th
  %tbody
    - @bedroom_groups.sort_by(&:count).each do |group|
      %tr
        %td.center.aligned= group.name
        %td.center.aligned= group.square_footage_range
        %td.center.aligned= group.price_range
        %td
          = link_to 'Select',
            apply_floorplans_path(query.merge(bedrooms: group.count)),
            class: 'ui compact fluid blue button'
