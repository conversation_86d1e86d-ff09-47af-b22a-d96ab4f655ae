class Management::Vendors::SkuListsController < ManagementController
  before_action :set_vendor

  def show
    respond_to do |format|
      format.xlsx { show_xlsx }
    end
  end

  def create
    upload = params[:upload]

    result = SkuListImporter.call \
      vendor: @vendor, path: upload.path

    flash[:error] = result.errors unless result.successful?

    redirect_to vendor_path(@vendor, anchor: '/sku_list')
  end

  def destroy
    @vendor.sku_list_items.destroy_all
    redirect_to vendor_path(@vendor, anchor: '/sku_list')
  end

  private

  def set_vendor
    @vendor = VendorsQuery.for_user(current_property_manager)
                          .find(params[:vendor_id])
  end

  def show_xlsx
    exporter = SkuListExporter.new(vendor: @vendor)

    send_data exporter.workbook.stream.string,
              filename: exporter.filename,
              type: :xlsx
  end
end
