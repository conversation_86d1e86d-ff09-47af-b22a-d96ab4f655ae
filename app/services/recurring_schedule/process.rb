class RecurringSchedule::Process
  extend Service

  def initialize(recurring_schedule:, effective_date: Time.zone.today)
    @recurring_schedule = recurring_schedule
    @effective_date = effective_date
  end

  def call
    case template
    when Invoice then process_invoice_template
    when Plutus::Entry then process_journal_entry_template
    when nil
      OpenStruct.new(successful?: false, errors: ['Orphan schedule'])
    else
      OpenStruct.new(successful?: false, errors: ['Unknown template'])
    end
  end

  private

  attr_reader :recurring_schedule

  delegate :template, to: :recurring_schedule

  def process_invoice_template
    ActiveRecord::Base.transaction do
      duplicate = template.amoeba_dup

      today = @effective_date

      net_d = template.due_date - template.physical_date

      duplicate.post_date = today
      duplicate.physical_date = today
      duplicate.due_date = today + net_d

      duplicate.save!

      create_forwards(duplicate)

      recurring_schedule.update!(last_ran_at: Time.zone.now)

      OpenStruct.new(successful?: true, invoice: duplicate)
    end
  rescue ActiveRecord::RecordInvalid => e
    OpenStruct.new(successful?: false, errors: e.record.errors.full_messages)
  end

  def create_forwards(duplicate)
    # This loop prepares the virtual line item attributes needed for
    # Invoice::Forward, as if this invoice had been submitted via the form.
    # It sets forward_id, markup_kind, and markup_raw.
    template.line_items.zip(duplicate.line_items).each do |oli, dli|
      sourcing_markup = oli.sourcing_markup

      next unless sourcing_markup

      forwarded_invoice = sourcing_markup.destination_item.invoice

      dli.forward_id = forwarded_invoice.buyer.to_sgid.to_s

      dli.markup_kind = if sourcing_markup.percentage_markup.present?
                          'percent'
                        else
                          'fixed'
                        end

      dli.markup_raw = if sourcing_markup.percentage_markup.present?
                         sourcing_markup.percentage_markup * 100
                       else
                         sourcing_markup.flat_markup
                       end
    end

    Invoice::Forward.call(duplicate)
  end

  def process_journal_entry_template
    ActiveRecord::Base.transaction do
      duplicate = duplicate_entry(template)

      duplicate.date = @effective_date

      duplicate.save!

      recurring_schedule.update!(last_ran_at: Time.zone.now)

      OpenStruct.new(successful?: true, journal_entry: duplicate)
    end
  rescue ActiveRecord::RecordInvalid => e
    OpenStruct.new(successful?: false, errors: e.record.errors.full_messages)
  end

  # Ideally this would work completely with amoeba, but alas this is done
  # manually for now. Partially I think this is because Plutus::Entry has
  # #amounts, #debit_amounts, and #credit_amounts. Also some of the amounts
  # seem to keep the old entry in memory, and think that they are for an older
  # date, and bump up against locked periods.
  def duplicate_entry(entry)
    duplicate = entry.amoeba_dup

    debits = []
    credits = []

    entry.amounts.each do |amount|
      attributes = {
        account_id: amount.account_id,
        amount: amount.amount,
        note: amount.note
      }

      if amount.credit?
        credits << attributes
      else
        debits << attributes
      end
    end

    duplicate.amounts = []
    duplicate.debit_amounts = []
    duplicate.credit_amounts = []
    duplicate.debits = debits
    duplicate.credits = credits

    duplicate
  end
end
