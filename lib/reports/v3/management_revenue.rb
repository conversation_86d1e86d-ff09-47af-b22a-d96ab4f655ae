class Reports::V3::ManagementRevenue < Reports::V3::Report
  def self.available_filters
    %i[customer_portfolio_activity date_range display]
  end

  def self.default_filter_values
    {
      start_date: Time.zone.today.last_month.beginning_of_month,
      end_date: Time.zone.today.last_month.end_of_month,
      basis: 'cash',
      display: 'detail'
    }
  end

  def self.display_values
    [['Detail', 'detail'], ['Summary', 'summary']]
  end

  def title
    'Management Revenue'
  end

  def expense_to_revenue_map
    if Customer.current_subdomain == 'gebraelmgmt'
      Accounting::InverseAccounts::Gebrael::CHART_3_EXPENSE_TO_REVENUE_MAPPING
    else
      {}
    end
  end

  def account_balance(account)
    @account_balances ||= begin
      matrix ||= Accounting::AccountBalanceMatrix.new(
        accounting_context: accounting_context,
        start_date: filters.start_date,
        end_date: filters.end_date
      )

      balances = {}

      accounts.each do |account|
        debit_balance = matrix.balance_for_customer_portfolio_filter(
          account: account,
          filter: filters.customer_portfolio_id.presence
        )

        balance = if account.credit_normal?
                    -debit_balance
                  else
                    debit_balance
                  end

        income_statement_balance = -balance

        balances[account.id] = Money.new(income_statement_balance)
      end

      balances
    end

    @account_balances[account.id]
  end

  def rows
    @rows ||= Plutus.in_cash_basis(journal_cache_hint: nil) do
      @total_rollup = Money.zero
      @total_passthrough = Money.zero
      @total_other = Money.zero

      rollup_revenue_accounts = rollup_accounts.where(type: 'Plutus::Revenue')

      [
        rollup_header,
        *rollup_revenue_accounts.flat_map { |account| rollup_rows(account) }.compact,
        rollup_total,
        passthrough_header,
        *passthrough_accounts.filter_map { |account| passthrough_row(account) },
        passthrough_total,
        other_header,
        *other_accounts.flat_map { |account| other_row(account) }.compact,
        other_total,
        net_income
      ]
    end
  end

  def rollup_rows(account)
    balance = account_balance(account)

    expense_ids = []

    expense_to_revenue_map.each do |exp_id, rev_id|
      expense_ids << exp_id if rev_id == account.id
    end

    expense_accounts = accounts.select { |expense| expense_ids.include?(expense.id) }

    less_expenses = Money.sum(
      expense_accounts.filter_map do |expense|
        account_balance(expense)
      end
    )

    return if balance.zero? && less_expenses.zero?

    net_balance = balance + less_expenses

    @total_rollup += net_balance

    if detail_display?
      [
        account_row(account, balance, 1),
        *expense_accounts.filter_map do |account|
          balance = account_balance(account)

          next if balance.zero?

          {
            cells: [
              { value: "Less #{account.display_name}", truncate: false, depth: 2 },
              { value: balance }
            ]
          }
        end,
        {
          cells: [
            { value: "Net #{account.name}", truncate: false, depth: 2, type: 'b' },
            { value: net_balance, type: 'b' }
          ]
        }
      ]
    else
      {
        cells: [
          { value: "Net #{account.name}", truncate: false, depth: 1 },
          { value: net_balance }
        ]
      }
    end
  end

  def passthrough_row(account)
    balance = account_balance(account)

    return if balance.zero?

    @total_passthrough += balance

    account_row(account, balance, 1)
  end

  def other_row(account)
    balance = account_balance(account)

    return if balance.zero?

    @total_other += balance

    account_row(account, balance, 1)
  end

  def section_header(name)
    { cells: [{ value: name, type: 'h3', colspan: 2 }] }
  end

  def account_row(account, balance, depth)
    {
      cells: [
        { value: account.display_name, truncate: false, depth: depth },
        { value: balance }
      ]
    }
  end

  def section_total(name, value, depth: 1, overline: 'single')
    {
      cells: [
        { value: name, type: 'b', depth: depth, overline: overline },
        { value: value, type: 'b', overline: overline }
      ]
    }
  end

  def rollup_header
    section_header('Recovered Activity')
  end

  def rollup_total
    section_total('Total Mapped', @total_rollup)
  end

  def passthrough_header
    section_header('Passthrough Activity')
  end

  def passthrough_total
    section_total('Total Passthrough', @total_passthrough)
  end

  def other_header
    section_header('Other Activity')
  end

  def other_total
    section_total('Total Other', @total_other)
  end

  def net_income
    value = @total_rollup + @total_passthrough + @total_other

    section_total('Net Income', value, depth: 0, overline: 'double')
  end

  def management_company
    Customer.current.client_entity
  end

  def passthrough_accounts
    accounts.where(passthrough: true)
  end

  def rollup_account_ids
    expense_to_revenue_map.values + expense_to_revenue_map.keys
  end

  def rollup_accounts
    accounts.where(passthrough: false, id: rollup_account_ids)
  end

  def other_accounts
    accounts.where(passthrough: false).where.not(id: rollup_account_ids)
  end

  def accounts
    management_company.accounts.reorder(gl_code: :asc, name: :asc, id: :asc).where(type: ['Plutus::Revenue', 'Plutus::Expense'])
  end

  def detail_display?
    filters[:display] == 'detail'
  end

  delegate :accounting_context, to: :management_company
end
