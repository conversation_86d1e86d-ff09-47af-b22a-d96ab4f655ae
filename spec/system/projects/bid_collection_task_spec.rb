require 'rails_helper'

RSpec.describe 'a bid collection task', :js,
               capybara_login: :property_manager do
  let(:project) { create(:project, members: [manager]) }

  scenario 'a user creates a bid collection task' do
    visit operations_project_path(project)

    click_on 'Add Task'

    fill_in 'Name', with: 'Bid Collection'
    fill_in 'Description', with: 'Collect at least one bid for roofing'

    find('.kind.picker .button', text: 'Bid Collection').click

    click_on 'Create'

    expect(page).to have_content(/task created successfully/i)
    expect(project.tasks.last).to be_bid_collection
  end

  context 'an existing bid collection task' do
    let!(:task) { create(:task, kind: :bid_collection, project: project) }
    let!(:vendor) { create(:vendor) }
    let!(:bid) { nil }

    before { visit operations_project_task_path(project, task) }

    scenario 'a user adds a bid' do
      amount = '$3,750.50'

      find('.button', text: 'Add Bid').click

      select_dropdown 'Vendor', vendor.name
      fill_in 'Amount', with: amount

      click_on 'Save'

      bid = task.bids.last
      expect(bid.amount.format).to eq(amount)
      expect(bid.vendor).to eq(vendor)
    end

    context 'with an existing bid' do
      let!(:bid) { create(:project_bid, task: task) }

      scenario 'a user removes a bid' do
        find('.vertical.ellipsis.icon').click
        sleep(0.5)
        expect do
          accept_confirm { click_on 'Remove' }
          expect(page).to have_no_content(bid.amount.format)
        end.to change { task.bids.count }.by(-1)
      end
    end
  end
end
