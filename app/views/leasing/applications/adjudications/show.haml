- if Feature.enabled?(:application_scorecard, Customer.current)
  = link_to leasing_application_scorecard_path(@application),
    class: 'right floated ui button' do
    %i.table.icon
    View Scorecard

.ui.breadcrumb
  = link_to 'Leads', leasing_leads_path, class: 'section'
  %i.right.angle.icon.divider

  = link_to @application.primary_tenant.name,
    leasing_lead_path(@application.primary_tenant),
    class: 'section'
  %i.right.angle.icon.divider

  = link_to 'Application',
    leasing_application_path(@application),
    class: 'section'
  %i.right.angle.icon.divider

  %h1.ui.header
    Adjudication

- if @application.awaiting_results?
  = render partial: 'awaiting_results_table',
    locals: { application: @application }

- else
  = render partial: 'results_table',
    locals: { application: @application }

= render partial: 'form',
  locals: { application: @application }
