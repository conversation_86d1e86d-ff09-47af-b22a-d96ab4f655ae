class BroadcastMailer < ApplicationMailer
  layout 'leasing_mailer'

  helper_method :brand

  def send_broadcast(recipient, broadcast)
    options = { to: recipient.email,
                content_type: 'text/html',
                subject: broadcast.subject,
                from: from_email(broadcast) }
    options[:reply_to] = broadcast.reply_to if broadcast.reply_to.present?
    @body = broadcast.body
    mail(options)
  end

  private

  def brand
    Customer.current.brand
  end

  def from_email(broadcast)
    author = broadcast.author
    email = author.email
    name = author.name

    if Feature.enabled?(:broadcasts_revela_email, Customer.current)
      email = "noreply@#{Customer.current_subdomain}.revela.co"
    end

    email_address_with_name(email, name)
  end
end
