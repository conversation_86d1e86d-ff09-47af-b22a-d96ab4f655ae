%table.ui.very.basic.single.line.selectable.table
  %tbody
    %thead
      %th.center.aligned Policy
      %th.center.aligned Period
      %th.center.aligned Document
      %th.center.aligned Contact
      %th.center.aligned Email
      %th.center.aligned Phone
      %th.center.aligned Status
      %th.center.aligned Actions

    - policies.each do |policy|
      %tr
        %td
          = policy.provider
          \/
          = policy.policy_number
        %td.center.aligned
          = policy.start_date.to_fs(:short_date)
          \-
          = policy.end_date.to_fs(:short_date)
        %td.center.aligned
          - if policy.document
            = link_to 'Download', policy.document.expiring_url
          - else
            None
        %td.center.aligned= policy.agent_name
        %td.center.aligned= policy.agent_email
        %td.center.aligned= policy.formatted_agent_phone
        %td.center.aligned
          - if policy.active?
            Active
          - else
            Inactive
        %td.center.aligned
          = link_to 'Edit',
            '#',
            data: { modal: :"insurance_information_modal_#{policy.id}" }
          &emsp;
          = link_to 'Remove',
            remove_policy_path(policy),
            method: :patch, data: { confirm: 'Remove Policy?' }
