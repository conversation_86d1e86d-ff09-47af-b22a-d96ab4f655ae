class Accounting::GeneralLedgerImportsController < AccountingController
  before_action :set_journal
  before_action :update_account_translations, only: :create

  def new
    session[:account_translations] = nil
  end

  def create
    path = params[:upload].path

    result = GeneralLedgerImporter.import \
      journal: @journal, path: path,
      account_translations: session[:account_translations],
      period_start: Date.parse(params[:period_start]),
      period_end: Date.parse(params[:period_end])

    if result.successful?
      count = result.entries.count
      flash[:success] = \
        "Imported #{count} #{'Entry'.pluralize(count)} Successfully"
      redirect_to accounting_journal_path(journal)
    elsif result.unknown_identifiers&.any?
      @unknown_identifiers = result.unknown_identifiers
      render :new
    else
      flash.now[:error] = result.errors
      render :new
    end
  end

  private

  attr_reader :journal

  def set_journal
    @journal = Company.find(params[:journal_id])
  end

  def update_account_translations
    (session[:account_translations] ||= {}).merge!(
      params[:account_translations]&.to_unsafe_h || {}
    )
  end
end
