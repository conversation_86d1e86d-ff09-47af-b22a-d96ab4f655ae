class ChargeSchedule < ApplicationRecord
  include Proratable

  belongs_to :chargeable, polymorphic: true, optional: false

  has_many :entries,
           class_name: 'ChargeSchedule::Entry',
           inverse_of: :charge_schedule,
           autosave: true,
           dependent: :destroy
  has_many :allocations, through: :entries
  has_many :lease_memberships, through: :allocations

  validates :chargeable, presence: true

  amoeba do
    include_association :entries
  end

  audited associated_with: :chargeable, except: :invoiced_through

  delegate :start_date, :end_date, to: :chargeable

  def needs_billing?(period)
    if invoiced_through.present? && period.end_of_month <= invoiced_through
      return false
    end

    if chargeable.is_a?(Lease)
      lease = chargeable

      # TODO: unit part redundant + consider lease archive date?
      return false if lease.archived? || lease.unit.archived?
    end

    # TODO: move to service
    billable_amount(period).positive?
  end

  def billable_amount(period)
    Money.sum(
      entries.map do |entry|
        proratable_delegate = OpenStruct.new(
          proration: proration,
          start_date: entry.start_date,
          end_date: entry.end_date,
          lease: (chargeable if chargeable.is_a?(Lease) && entry.inherit_term?)
        )

        Accounting::Prorater.new(proratable_delegate, period).call(entry.amount)
      end
    )
  end

  def active_recurring_entries(as_of = Time.zone.today)
    entries.recurring.active(as_of.clamp(start_date, end_date))
  end
end
